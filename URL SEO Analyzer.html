<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free URL SEO Analyzer - Check URL Structure for SEO Optimization</title>
    <meta name="description" content="Analyze your URLs for SEO optimization with our free URL SEO Analyzer. Check URL structure, length, keywords, and get recommendations for better search rankings.">
    <meta name="keywords" content="URL SEO analyzer, SEO friendly URL, URL structure, URL optimization, clean URLs, SEO URL checker, URL best practices">
    <link rel="canonical" href="https://www.webtoolskit.org/p/url-seo-analyzer.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free URL SEO Analyzer - Check URL Structure for SEO Optimization",
        "description": "Analyze your URLs for SEO optimization with our free URL SEO Analyzer. Check URL structure, length, keywords, and get recommendations for better search rankings.",
        "url": "https://www.webtoolskit.org/p/url-seo-analyzer.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "URL SEO Analyzer",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "URL structure analysis",
                "SEO-friendly URL checking",
                "URL length optimization",
                "Keyword analysis in URLs",
                "URL best practices recommendations"
            ]
        },
        "potentialAction": [
            { "@type": "AnalyzeAction", "name": "Analyze URL Structure" },
            { "@type": "CheckAction", "name": "Check URL SEO Optimization" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What makes a URL SEO-friendly?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An SEO-friendly URL is short, descriptive, readable, and contains relevant keywords. It should use hyphens to separate words, avoid special characters and parameters when possible, use lowercase letters, and clearly indicate the page content. Good URLs help both users and search engines understand what the page is about."
          }
        },
        {
          "@type": "Question",
          "name": "What is the ideal URL length for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The ideal URL length is under 60 characters, though URLs up to 100 characters are generally acceptable. Shorter URLs are easier to share, remember, and display properly in search results. Google can handle longer URLs, but shorter ones tend to perform better for user experience and SEO."
          }
        },
        {
          "@type": "Question",
          "name": "Should I use hyphens or underscores in URLs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Always use hyphens (-) instead of underscores (_) in URLs. Google treats hyphens as word separators, making 'seo-friendly-url' readable as three separate words. Underscores are treated as word joiners, so 'seo_friendly_url' is seen as one long word, which is less SEO-friendly."
          }
        },
        {
          "@type": "Question",
          "name": "How do keywords in URLs affect SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Keywords in URLs provide a small SEO benefit and help users understand page content. Include your primary keyword in the URL when it makes sense, but avoid keyword stuffing. The URL should be natural and descriptive rather than packed with keywords, as user experience is more important than keyword density in URLs."
          }
        },
        {
          "@type": "Question",
          "name": "What URL elements should I avoid for better SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Avoid dynamic parameters (?id=123), special characters (!@#$%), uppercase letters, underscores, stop words (a, an, the), excessive subdirectories, and very long URLs. Also avoid duplicate content issues by using canonical URLs and implementing proper redirects for URL variations."
          }
        }
      ]
    }
    </script>

    <style>
        /* URL SEO Analyzer Widget - Simplified & Template Compatible */
        .url-seo-analyzer-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .url-seo-analyzer-widget-container * { box-sizing: border-box; }

        .url-seo-analyzer-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .url-seo-analyzer-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .url-seo-analyzer-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .url-seo-analyzer-field {
            display: flex;
            flex-direction: column;
        }

        .url-seo-analyzer-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .url-seo-analyzer-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .url-seo-analyzer-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .url-seo-analyzer-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .url-seo-analyzer-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .url-seo-analyzer-btn:hover { transform: translateY(-2px); }

        .url-seo-analyzer-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .url-seo-analyzer-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .url-seo-analyzer-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .url-seo-analyzer-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .url-seo-analyzer-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .url-seo-analyzer-results.show {
            display: block;
        }

        .url-seo-analyzer-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .url-seo-analyzer-score-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
        }

        .url-seo-analyzer-score-value {
            font-size: 3rem;
            font-weight: 800;
            margin-right: var(--spacing-md);
        }

        .url-seo-analyzer-score-value.excellent {
            color: #10b981;
        }

        .url-seo-analyzer-score-value.good {
            color: #3b82f6;
        }

        .url-seo-analyzer-score-value.fair {
            color: #f59e0b;
        }

        .url-seo-analyzer-score-value.poor {
            color: #dc2626;
        }

        .url-seo-analyzer-score-label {
            font-size: 1.125rem;
            color: var(--text-color-light);
        }

        .url-seo-analyzer-checks {
            display: grid;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .url-seo-analyzer-check-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .url-seo-analyzer-check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .url-seo-analyzer-check-icon.pass {
            background-color: #10b981;
        }

        .url-seo-analyzer-check-icon.fail {
            background-color: #dc2626;
        }

        .url-seo-analyzer-check-icon.warning {
            background-color: #f59e0b;
        }

        .url-seo-analyzer-check-text {
            flex: 1;
            color: var(--text-color);
        }

        .url-seo-analyzer-recommendations {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        .url-seo-analyzer-recommendations h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
        }

        .url-seo-analyzer-recommendation-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .url-seo-analyzer-recommendation-item {
            padding: var(--spacing-xs) 0;
            color: var(--text-color-light);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .url-seo-analyzer-recommendation-item:before {
            content: "•";
            color: var(--primary-color);
            margin-right: var(--spacing-xs);
        }

        .url-seo-analyzer-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .url-seo-analyzer-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .url-seo-analyzer-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .url-seo-analyzer-widget-title { font-size: 1.875rem; }
            .url-seo-analyzer-buttons { flex-direction: column; }
            .url-seo-analyzer-btn { flex: none; }
            .url-seo-analyzer-score-display { flex-direction: column; text-align: center; }
            .url-seo-analyzer-score-value { margin-right: 0; margin-bottom: var(--spacing-sm); }
        }

        [data-theme="dark"] .url-seo-analyzer-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .url-seo-analyzer-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .url-seo-analyzer-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="keyword-density-checker"] .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="sitemap-generator"] .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .url-seo-analyzer-related-tool-item:hover .url-seo-analyzer-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="keyword-density-checker"]:hover .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #f7ac2e, #e28417); }
        a[href*="sitemap-generator"]:hover .url-seo-analyzer-related-tool-icon { background: linear-gradient(145deg, #f05eab, #e43887); }

        .url-seo-analyzer-related-tool-item { box-shadow: none; border: none; }
        .url-seo-analyzer-related-tool-item:hover { box-shadow: none; border: none; }
        .url-seo-analyzer-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .url-seo-analyzer-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .url-seo-analyzer-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .url-seo-analyzer-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .url-seo-analyzer-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .url-seo-analyzer-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .url-seo-analyzer-related-tool-item:hover .url-seo-analyzer-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .url-seo-analyzer-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .url-seo-analyzer-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .url-seo-analyzer-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .url-seo-analyzer-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .url-seo-analyzer-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .url-seo-analyzer-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .url-seo-analyzer-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .url-seo-analyzer-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .url-seo-analyzer-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .url-seo-analyzer-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .url-seo-analyzer-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .url-seo-analyzer-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .url-seo-analyzer-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .url-seo-analyzer-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="url-seo-analyzer-widget-container">
        <h1 class="url-seo-analyzer-widget-title">URL SEO Analyzer</h1>
        <p class="url-seo-analyzer-widget-description">
            Analyze your URLs for SEO optimization. Check URL structure, length, keywords, and get actionable recommendations to improve your search engine rankings.
        </p>
        
        <form class="url-seo-analyzer-form">
            <div class="url-seo-analyzer-field">
                <label for="urlToAnalyze" class="url-seo-analyzer-label">URL to Analyze:</label>
                <input 
                    type="url" 
                    id="urlToAnalyze" 
                    class="url-seo-analyzer-input"
                    placeholder="https://example.com/your-page-url"
                />
            </div>

            <div class="url-seo-analyzer-field">
                <label for="targetKeyword" class="url-seo-analyzer-label">Target Keyword (Optional):</label>
                <input 
                    type="text" 
                    id="targetKeyword" 
                    class="url-seo-analyzer-input"
                    placeholder="Enter your target keyword to check if it's in the URL"
                />
            </div>
        </form>

        <div class="url-seo-analyzer-buttons">
            <button class="url-seo-analyzer-btn url-seo-analyzer-btn-primary" onclick="URLSEOAnalyzer.analyze()">
                Analyze URL
            </button>
            <button class="url-seo-analyzer-btn url-seo-analyzer-btn-secondary" onclick="URLSEOAnalyzer.clear()">
                Clear All
            </button>
        </div>

        <div class="url-seo-analyzer-results" id="analysisResults">
            <h3 class="url-seo-analyzer-results-title">SEO Analysis Results</h3>

            <div class="url-seo-analyzer-score-display">
                <div class="url-seo-analyzer-score-value excellent" id="seoScore">0</div>
                <div class="url-seo-analyzer-score-label">
                    SEO Score<br>
                    <small id="scoreDescription">out of 100</small>
                </div>
            </div>

            <div class="url-seo-analyzer-checks" id="seoChecks">
                <!-- SEO checks will be populated here -->
            </div>

            <div class="url-seo-analyzer-recommendations" id="recommendations">
                <h4>Recommendations for Improvement:</h4>
                <ul class="url-seo-analyzer-recommendation-list" id="recommendationList">
                    <li class="url-seo-analyzer-recommendation-item">Enter a URL to see personalized recommendations.</li>
                </ul>
            </div>
        </div>

        <div class="url-seo-analyzer-related-tools">
            <h3 class="url-seo-analyzer-related-tools-title">Related Tools</h3>
            <div class="url-seo-analyzer-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="url-seo-analyzer-related-tool-item" rel="noopener">
                    <div class="url-seo-analyzer-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="url-seo-analyzer-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/keyword-density-checker.html" class="url-seo-analyzer-related-tool-item" rel="noopener">
                    <div class="url-seo-analyzer-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="url-seo-analyzer-related-tool-name">Keyword Density Checker</div>
                </a>

                <a href="/p/sitemap-generator.html" class="url-seo-analyzer-related-tool-item" rel="noopener">
                    <div class="url-seo-analyzer-related-tool-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="url-seo-analyzer-related-tool-name">Sitemap Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional URL SEO Analysis for Better Rankings</h2>
            <p>Our <strong>URL SEO Analyzer</strong> evaluates your URLs against SEO best practices to help improve your search engine rankings. A well-optimized URL structure is crucial for both user experience and search engine crawling, making it easier for Google to understand and rank your content.</p>
            <p>Whether you're optimizing existing pages or planning new content, our tool provides comprehensive analysis of URL length, structure, keywords, and readability. Get actionable recommendations to create SEO-friendly URLs that boost your website's visibility and performance.</p>

            <h3>How to Use the URL SEO Analyzer</h3>
            <ol>
                <li><strong>Enter Your URL:</strong> Paste the complete URL you want to analyze in the input field.</li>
                <li><strong>Add Target Keyword:</strong> Optionally enter your target keyword to check if it's included in the URL.</li>
                <li><strong>Analyze URL:</strong> Click "Analyze URL" to get a comprehensive SEO evaluation and score.</li>
                <li><strong>Review Results:</strong> Check your SEO score, individual checks, and follow the recommendations for improvement.</li>
            </ol>

            <h3>Frequently Asked Questions About URL SEO</h3>

            <h4>What makes a URL SEO-friendly?</h4>
            <p>An SEO-friendly URL is short, descriptive, readable, and contains relevant keywords. It should use hyphens to separate words, avoid special characters and parameters when possible, use lowercase letters, and clearly indicate the page content. Good URLs help both users and search engines understand what the page is about.</p>

            <h4>What is the ideal URL length for SEO?</h4>
            <p>The ideal URL length is under 60 characters, though URLs up to 100 characters are generally acceptable. Shorter URLs are easier to share, remember, and display properly in search results. Google can handle longer URLs, but shorter ones tend to perform better for user experience and SEO.</p>

            <h4>Should I use hyphens or underscores in URLs?</h4>
            <p>Always use hyphens (-) instead of underscores (_) in URLs. Google treats hyphens as word separators, making 'seo-friendly-url' readable as three separate words. Underscores are treated as word joiners, so 'seo_friendly_url' is seen as one long word, which is less SEO-friendly.</p>

            <h4>How do keywords in URLs affect SEO?</h4>
            <p>Keywords in URLs provide a small SEO benefit and help users understand page content. Include your primary keyword in the URL when it makes sense, but avoid keyword stuffing. The URL should be natural and descriptive rather than packed with keywords, as user experience is more important than keyword density in URLs.</p>

            <h4>What URL elements should I avoid for better SEO?</h4>
            <p>Avoid dynamic parameters (?id=123), special characters (!@#$%), uppercase letters, underscores, stop words (a, an, the), excessive subdirectories, and very long URLs. Also avoid duplicate content issues by using canonical URLs and implementing proper redirects for URL variations.</p>
        </div>

        <div class="url-seo-analyzer-features">
            <h3 class="url-seo-analyzer-features-title">Key Features:</h3>
            <ul class="url-seo-analyzer-features-list">
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">Comprehensive URL Analysis</li>
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">SEO Score Calculation</li>
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">URL Length Optimization</li>
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">Keyword Presence Detection</li>
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">Structure Best Practices Check</li>
                <li class="url-seo-analyzer-features-item" style="margin-bottom: 0.3em;">Actionable Recommendations</li>
                <li class="url-seo-analyzer-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="url-seo-analyzer-notification" id="urlSeoNotification">
        ✓ URL analysis completed!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                urlToAnalyze: () => document.getElementById('urlToAnalyze'),
                targetKeyword: () => document.getElementById('targetKeyword'),
                analysisResults: () => document.getElementById('analysisResults'),
                seoScore: () => document.getElementById('seoScore'),
                scoreDescription: () => document.getElementById('scoreDescription'),
                seoChecks: () => document.getElementById('seoChecks'),
                recommendationList: () => document.getElementById('recommendationList'),
                notification: () => document.getElementById('urlSeoNotification')
            };

            function analyzeURL(url, keyword = '') {
                const analysis = {
                    score: 0,
                    checks: [],
                    recommendations: []
                };

                try {
                    const urlObj = new URL(url);
                    const pathname = urlObj.pathname;
                    const search = urlObj.search;

                    // Check URL length
                    const urlLength = url.length;
                    if (urlLength <= 60) {
                        analysis.checks.push({ text: 'URL length is optimal (≤60 characters)', status: 'pass' });
                        analysis.score += 20;
                    } else if (urlLength <= 100) {
                        analysis.checks.push({ text: 'URL length is acceptable (≤100 characters)', status: 'warning' });
                        analysis.score += 10;
                        analysis.recommendations.push('Consider shortening the URL for better user experience');
                    } else {
                        analysis.checks.push({ text: 'URL is too long (>100 characters)', status: 'fail' });
                        analysis.recommendations.push('Shorten the URL to under 100 characters for better SEO');
                    }

                    // Check for HTTPS
                    if (urlObj.protocol === 'https:') {
                        analysis.checks.push({ text: 'Uses secure HTTPS protocol', status: 'pass' });
                        analysis.score += 15;
                    } else {
                        analysis.checks.push({ text: 'Not using secure HTTPS protocol', status: 'fail' });
                        analysis.recommendations.push('Switch to HTTPS for better security and SEO');
                    }

                    // Check for hyphens vs underscores
                    const hasHyphens = pathname.includes('-');
                    const hasUnderscores = pathname.includes('_');
                    if (hasHyphens && !hasUnderscores) {
                        analysis.checks.push({ text: 'Uses hyphens for word separation', status: 'pass' });
                        analysis.score += 10;
                    } else if (hasUnderscores) {
                        analysis.checks.push({ text: 'Uses underscores instead of hyphens', status: 'fail' });
                        analysis.recommendations.push('Replace underscores with hyphens for better SEO');
                    } else {
                        analysis.checks.push({ text: 'No word separators found', status: 'warning' });
                        analysis.score += 5;
                    }

                    // Check for uppercase letters
                    if (pathname === pathname.toLowerCase()) {
                        analysis.checks.push({ text: 'Uses lowercase letters', status: 'pass' });
                        analysis.score += 10;
                    } else {
                        analysis.checks.push({ text: 'Contains uppercase letters', status: 'fail' });
                        analysis.recommendations.push('Use lowercase letters in URLs for consistency');
                    }

                    // Check for parameters
                    if (!search || search === '?') {
                        analysis.checks.push({ text: 'Clean URL without parameters', status: 'pass' });
                        analysis.score += 15;
                    } else {
                        analysis.checks.push({ text: 'Contains URL parameters', status: 'warning' });
                        analysis.score += 5;
                        analysis.recommendations.push('Consider using clean URLs without parameters when possible');
                    }

                    // Check for special characters
                    const specialChars = /[!@#$%^&*()+=\[\]{}|\\:";'<>?,]/;
                    if (!specialChars.test(pathname)) {
                        analysis.checks.push({ text: 'No special characters in URL', status: 'pass' });
                        analysis.score += 10;
                    } else {
                        analysis.checks.push({ text: 'Contains special characters', status: 'fail' });
                        analysis.recommendations.push('Remove special characters from URL');
                    }

                    // Check for keyword presence
                    if (keyword.trim()) {
                        const keywordInUrl = pathname.toLowerCase().includes(keyword.toLowerCase().replace(/\s+/g, '-'));
                        if (keywordInUrl) {
                            analysis.checks.push({ text: 'Target keyword found in URL', status: 'pass' });
                            analysis.score += 15;
                        } else {
                            analysis.checks.push({ text: 'Target keyword not found in URL', status: 'warning' });
                            analysis.score += 5;
                            analysis.recommendations.push('Consider including your target keyword in the URL');
                        }
                    } else {
                        analysis.checks.push({ text: 'No target keyword specified', status: 'warning' });
                        analysis.score += 5;
                    }

                    // Check URL depth
                    const pathSegments = pathname.split('/').filter(segment => segment.length > 0);
                    if (pathSegments.length <= 3) {
                        analysis.checks.push({ text: 'Good URL depth (≤3 levels)', status: 'pass' });
                        analysis.score += 5;
                    } else if (pathSegments.length <= 5) {
                        analysis.checks.push({ text: 'Acceptable URL depth (≤5 levels)', status: 'warning' });
                        analysis.score += 2;
                    } else {
                        analysis.checks.push({ text: 'URL depth is too deep (>5 levels)', status: 'fail' });
                        analysis.recommendations.push('Reduce URL depth for better user experience');
                    }

                } catch (error) {
                    analysis.checks.push({ text: 'Invalid URL format', status: 'fail' });
                    analysis.recommendations.push('Please enter a valid URL starting with http:// or https://');
                }

                return analysis;
            }

            function getScoreClass(score) {
                if (score >= 85) return 'excellent';
                if (score >= 70) return 'good';
                if (score >= 50) return 'fair';
                return 'poor';
            }

            function getScoreDescription(score) {
                if (score >= 85) return 'Excellent SEO optimization';
                if (score >= 70) return 'Good SEO optimization';
                if (score >= 50) return 'Fair SEO optimization';
                return 'Needs improvement';
            }

            window.URLSEOAnalyzer = {
                analyze() {
                    const url = elements.urlToAnalyze().value.trim();
                    const keyword = elements.targetKeyword().value.trim();

                    if (!url) {
                        this.showNotification('Please enter a URL to analyze.');
                        return;
                    }

                    const analysis = analyzeURL(url, keyword);

                    // Update score display
                    const scoreElement = elements.seoScore();
                    scoreElement.textContent = analysis.score;
                    scoreElement.className = `url-seo-analyzer-score-value ${getScoreClass(analysis.score)}`;
                    elements.scoreDescription().innerHTML = `${getScoreDescription(analysis.score)}<br><small>out of 100</small>`;

                    // Update checks
                    let checksHTML = '';
                    analysis.checks.forEach(check => {
                        const icon = check.status === 'pass' ? '✓' : check.status === 'warning' ? '!' : '✗';
                        checksHTML += `
                            <div class="url-seo-analyzer-check-item">
                                <div class="url-seo-analyzer-check-icon ${check.status}">${icon}</div>
                                <div class="url-seo-analyzer-check-text">${check.text}</div>
                            </div>
                        `;
                    });
                    elements.seoChecks().innerHTML = checksHTML;

                    // Update recommendations
                    let recommendationsHTML = '';
                    if (analysis.recommendations.length === 0) {
                        recommendationsHTML = '<li class="url-seo-analyzer-recommendation-item">Great! Your URL follows SEO best practices.</li>';
                    } else {
                        analysis.recommendations.forEach(rec => {
                            recommendationsHTML += `<li class="url-seo-analyzer-recommendation-item">${rec}</li>`;
                        });
                    }
                    elements.recommendationList().innerHTML = recommendationsHTML;

                    // Show results
                    elements.analysisResults().classList.add('show');

                    this.showNotification('✓ URL analysis completed!');
                },

                clear() {
                    elements.urlToAnalyze().value = '';
                    elements.targetKeyword().value = '';
                    elements.analysisResults().classList.remove('show');

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        URLSEOAnalyzer.analyze();
                    }
                });
            });
        })();
    </script>
</body>
</html>
