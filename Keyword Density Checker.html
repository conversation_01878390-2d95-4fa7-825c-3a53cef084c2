<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Keyword Density Checker - Analyze Content for SEO Optimization</title>
    <meta name="description" content="Check keyword density and frequency with our free SEO tool. Analyze your content, avoid keyword stuffing, and optimize for better search engine rankings.">
    <meta name="keywords" content="keyword density checker, keyword frequency, keyword analysis, SEO content analysis, keyword stuffing checker, content optimization">
    <link rel="canonical" href="https://www.webtoolskit.org/p/keyword-density-checker.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Keyword Density Checker - Analyze Content for SEO Optimization",
        "description": "Check keyword density and frequency with our free SEO tool. Analyze your content, avoid keyword stuffing, and optimize for better search engine rankings.",
        "url": "https://www.webtoolskit.org/p/keyword-density-checker.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Keyword Density Checker",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Keyword density analysis",
                "Keyword frequency counting",
                "Content optimization recommendations",
                "Keyword stuffing detection",
                "SEO content analysis"
            ]
        },
        "potentialAction": [
            { "@type": "AnalyzeAction", "name": "Analyze Keyword Density" },
            { "@type": "CheckAction", "name": "Check Content Optimization" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is keyword density?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Keyword density is the percentage of times a target keyword or phrase appears in your content compared to the total number of words. It's calculated by dividing the number of keyword occurrences by the total word count and multiplying by 100. For example, if a keyword appears 5 times in a 500-word article, the keyword density is 1%."
          }
        },
        {
          "@type": "Question",
          "name": "What is the ideal keyword density for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Most SEO professionals recommend a keyword density of 1-2% for optimal results. This means your target keyword should appear 1-2 times per 100 words. However, there's no magic number - focus on natural, readable content rather than hitting a specific percentage. Google prioritizes content quality and user experience over keyword density."
          }
        },
        {
          "@type": "Question",
          "name": "What is keyword stuffing and why should I avoid it?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Keyword stuffing is the practice of overusing keywords in content to manipulate search rankings. This black-hat SEO technique can result in penalties from Google, poor user experience, and lower search rankings. Modern search engines are sophisticated enough to detect and penalize keyword stuffing, so focus on natural, valuable content instead."
          }
        },
        {
          "@type": "Question",
          "name": "How do I check keyword density in my content?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Keyword Density Checker is the easiest way. Simply paste your content into the text area, enter your target keyword, and click 'Analyze Content'. The tool will calculate keyword density, show frequency counts, and provide optimization recommendations to help you create SEO-friendly content."
          }
        },
        {
          "@type": "Question",
          "name": "Should I focus only on exact keyword matches?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, modern SEO benefits from using keyword variations, synonyms, and related terms. Search engines understand semantic relationships and context. While tracking exact keyword density is useful, also include related keywords, long-tail variations, and natural language that provides value to your readers."
          }
        }
      ]
    }
    </script>

    <style>
        /* Keyword Density Checker Widget - Simplified & Template Compatible */
        .keyword-density-checker-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .keyword-density-checker-widget-container * { box-sizing: border-box; }

        .keyword-density-checker-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .keyword-density-checker-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .keyword-density-checker-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .keyword-density-checker-field {
            display: flex;
            flex-direction: column;
        }

        .keyword-density-checker-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .keyword-density-checker-input,
        .keyword-density-checker-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .keyword-density-checker-textarea {
            resize: vertical;
            min-height: 200px;
        }

        .keyword-density-checker-input:focus,
        .keyword-density-checker-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .keyword-density-checker-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .keyword-density-checker-stat {
            text-align: center;
            padding: var(--spacing-sm);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .keyword-density-checker-stat-value {
            font-weight: 600;
            color: var(--text-color);
            font-size: 1.125rem;
        }

        .keyword-density-checker-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .keyword-density-checker-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .keyword-density-checker-btn:hover { transform: translateY(-2px); }

        .keyword-density-checker-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .keyword-density-checker-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .keyword-density-checker-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .keyword-density-checker-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .keyword-density-checker-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .keyword-density-checker-results.show {
            display: block;
        }

        .keyword-density-checker-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .keyword-density-checker-density-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
        }

        .keyword-density-checker-density-value {
            font-size: 3rem;
            font-weight: 800;
            margin-right: var(--spacing-md);
        }

        .keyword-density-checker-density-value.optimal {
            color: #10b981;
        }

        .keyword-density-checker-density-value.warning {
            color: #f59e0b;
        }

        .keyword-density-checker-density-value.error {
            color: #dc2626;
        }

        .keyword-density-checker-density-label {
            font-size: 1.125rem;
            color: var(--text-color-light);
        }

        .keyword-density-checker-recommendation {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-weight: 600;
        }

        .keyword-density-checker-recommendation.optimal {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .keyword-density-checker-recommendation.warning {
            background-color: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .keyword-density-checker-recommendation.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #dc2626;
        }

        .keyword-density-checker-keyword-list {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            max-height: 300px;
            overflow-y: auto;
        }

        .keyword-density-checker-keyword-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .keyword-density-checker-keyword-item:last-child {
            border-bottom: none;
        }

        .keyword-density-checker-keyword-text {
            font-weight: 600;
            color: var(--text-color);
        }

        .keyword-density-checker-keyword-stats {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .keyword-density-checker-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .keyword-density-checker-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .keyword-density-checker-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .keyword-density-checker-widget-title { font-size: 1.875rem; }
            .keyword-density-checker-buttons { flex-direction: column; }
            .keyword-density-checker-btn { flex: none; }
            .keyword-density-checker-density-display { flex-direction: column; text-align: center; }
            .keyword-density-checker-density-value { margin-right: 0; margin-bottom: var(--spacing-sm); }
            .keyword-density-checker-keyword-stats { flex-direction: column; gap: var(--spacing-xs); }
        }

        [data-theme="dark"] .keyword-density-checker-input:focus,
        [data-theme="dark"] .keyword-density-checker-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .keyword-density-checker-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .keyword-density-checker-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="title-meta-description-checker"] .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="meta-tag-generator"] .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="url-seo-analyzer"] .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .keyword-density-checker-related-tool-item:hover .keyword-density-checker-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="title-meta-description-checker"]:hover .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="meta-tag-generator"]:hover .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="url-seo-analyzer"]:hover .keyword-density-checker-related-tool-icon { background: linear-gradient(145deg, #7c3aed, #6366f1); }

        .keyword-density-checker-related-tool-item { box-shadow: none; border: none; }
        .keyword-density-checker-related-tool-item:hover { box-shadow: none; border: none; }
        .keyword-density-checker-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .keyword-density-checker-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .keyword-density-checker-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .keyword-density-checker-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .keyword-density-checker-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .keyword-density-checker-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .keyword-density-checker-related-tool-item:hover .keyword-density-checker-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .keyword-density-checker-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .keyword-density-checker-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .keyword-density-checker-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .keyword-density-checker-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .keyword-density-checker-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .keyword-density-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .keyword-density-checker-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .keyword-density-checker-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .keyword-density-checker-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .keyword-density-checker-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .keyword-density-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .keyword-density-checker-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .keyword-density-checker-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .keyword-density-checker-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="keyword-density-checker-widget-container">
        <h1 class="keyword-density-checker-widget-title">Keyword Density Checker</h1>
        <p class="keyword-density-checker-widget-description">
            Analyze your content's keyword density and frequency to optimize for SEO. Check for keyword stuffing, find the right balance, and improve your search engine rankings.
        </p>
        
        <form class="keyword-density-checker-form">
            <div class="keyword-density-checker-field">
                <label for="targetKeyword" class="keyword-density-checker-label">Target Keyword:</label>
                <input 
                    type="text" 
                    id="targetKeyword" 
                    class="keyword-density-checker-input"
                    placeholder="Enter your target keyword or phrase"
                />
            </div>

            <div class="keyword-density-checker-field">
                <label for="contentText" class="keyword-density-checker-label">Content to Analyze:</label>
                <textarea 
                    id="contentText" 
                    class="keyword-density-checker-textarea"
                    placeholder="Paste your content here to analyze keyword density and frequency..."
                ></textarea>
                <div class="keyword-density-checker-stats">
                    <div class="keyword-density-checker-stat">
                        <div class="keyword-density-checker-stat-value" id="wordCount">0</div>
                        <div>Words</div>
                    </div>
                    <div class="keyword-density-checker-stat">
                        <div class="keyword-density-checker-stat-value" id="charCount">0</div>
                        <div>Characters</div>
                    </div>
                    <div class="keyword-density-checker-stat">
                        <div class="keyword-density-checker-stat-value" id="paragraphCount">0</div>
                        <div>Paragraphs</div>
                    </div>
                </div>
            </div>
        </form>

        <div class="keyword-density-checker-buttons">
            <button class="keyword-density-checker-btn keyword-density-checker-btn-primary" onclick="KeywordDensityChecker.analyze()">
                Analyze Content
            </button>
            <button class="keyword-density-checker-btn keyword-density-checker-btn-secondary" onclick="KeywordDensityChecker.clear()">
                Clear All
            </button>
        </div>

        <div class="keyword-density-checker-results" id="analysisResults">
            <h3 class="keyword-density-checker-results-title">Analysis Results</h3>

            <div class="keyword-density-checker-density-display">
                <div class="keyword-density-checker-density-value optimal" id="densityValue">0%</div>
                <div class="keyword-density-checker-density-label">
                    Keyword Density<br>
                    <small id="keywordOccurrences">0 occurrences found</small>
                </div>
            </div>

            <div class="keyword-density-checker-recommendation optimal" id="recommendation">
                Enter content and a target keyword to see analysis results.
            </div>

            <h4 style="color: var(--text-color); margin-bottom: var(--spacing-md);">Top Keywords Found:</h4>
            <div class="keyword-density-checker-keyword-list" id="keywordList">
                <div style="text-align: center; color: var(--text-color-light); padding: var(--spacing-lg);">
                    No keywords analyzed yet. Add content and click "Analyze Content" to see results.
                </div>
            </div>
        </div>

        <div class="keyword-density-checker-related-tools">
            <h3 class="keyword-density-checker-related-tools-title">Related Tools</h3>
            <div class="keyword-density-checker-related-tools-grid">
                <a href="/p/title-meta-description-checker.html" class="keyword-density-checker-related-tool-item" rel="noopener">
                    <div class="keyword-density-checker-related-tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="keyword-density-checker-related-tool-name">Title Meta Description Checker</div>
                </a>

                <a href="/p/meta-tag-generator.html" class="keyword-density-checker-related-tool-item" rel="noopener">
                    <div class="keyword-density-checker-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="keyword-density-checker-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/url-seo-analyzer.html" class="keyword-density-checker-related-tool-item" rel="noopener">
                    <div class="keyword-density-checker-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="keyword-density-checker-related-tool-name">URL SEO Analyzer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Keyword Density Analysis for SEO Success</h2>
            <p>Our <strong>Keyword Density Checker</strong> helps you analyze and optimize your content's keyword usage for better search engine performance. By monitoring keyword frequency and density, you can avoid keyword stuffing while ensuring your target keywords appear naturally throughout your content.</p>
            <p>Whether you're a content creator, SEO professional, or digital marketer, our tool provides detailed insights into your content's keyword distribution. Get recommendations on optimal keyword usage and improve your content's SEO effectiveness with data-driven analysis.</p>

            <h3>How to Use the Keyword Density Checker</h3>
            <ol>
                <li><strong>Enter Target Keyword:</strong> Add the specific keyword or phrase you want to analyze in your content.</li>
                <li><strong>Paste Your Content:</strong> Add the text content you want to analyze in the textarea.</li>
                <li><strong>Analyze Content:</strong> Click "Analyze Content" to get detailed keyword density and frequency analysis.</li>
                <li><strong>Review Results:</strong> Check the density percentage, recommendations, and top keywords found in your content.</li>
            </ol>

            <h3>Frequently Asked Questions About Keyword Density</h3>

            <h4>What is keyword density?</h4>
            <p>Keyword density is the percentage of times a target keyword or phrase appears in your content compared to the total number of words. It's calculated by dividing the number of keyword occurrences by the total word count and multiplying by 100. For example, if a keyword appears 5 times in a 500-word article, the keyword density is 1%.</p>

            <h4>What is the ideal keyword density for SEO?</h4>
            <p>Most SEO professionals recommend a keyword density of 1-2% for optimal results. This means your target keyword should appear 1-2 times per 100 words. However, there's no magic number - focus on natural, readable content rather than hitting a specific percentage. Google prioritizes content quality and user experience over keyword density.</p>

            <h4>What is keyword stuffing and why should I avoid it?</h4>
            <p>Keyword stuffing is the practice of overusing keywords in content to manipulate search rankings. This black-hat SEO technique can result in penalties from Google, poor user experience, and lower search rankings. Modern search engines are sophisticated enough to detect and penalize keyword stuffing, so focus on natural, valuable content instead.</p>

            <h4>How do I check keyword density in my content?</h4>
            <p>Using our Keyword Density Checker is the easiest way. Simply paste your content into the text area, enter your target keyword, and click 'Analyze Content'. The tool will calculate keyword density, show frequency counts, and provide optimization recommendations to help you create SEO-friendly content.</p>

            <h4>Should I focus only on exact keyword matches?</h4>
            <p>No, modern SEO benefits from using keyword variations, synonyms, and related terms. Search engines understand semantic relationships and context. While tracking exact keyword density is useful, also include related keywords, long-tail variations, and natural language that provides value to your readers.</p>
        </div>

        <div class="keyword-density-checker-features">
            <h3 class="keyword-density-checker-features-title">Key Features:</h3>
            <ul class="keyword-density-checker-features-list">
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">Real-time Keyword Density Analysis</li>
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">Keyword Frequency Counting</li>
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">Content Statistics Display</li>
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">SEO Optimization Recommendations</li>
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">Top Keywords Discovery</li>
                <li class="keyword-density-checker-features-item" style="margin-bottom: 0.3em;">Keyword Stuffing Detection</li>
                <li class="keyword-density-checker-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="keyword-density-checker-notification" id="keywordDensityNotification">
        ✓ Analysis completed successfully!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                targetKeyword: () => document.getElementById('targetKeyword'),
                contentText: () => document.getElementById('contentText'),
                wordCount: () => document.getElementById('wordCount'),
                charCount: () => document.getElementById('charCount'),
                paragraphCount: () => document.getElementById('paragraphCount'),
                analysisResults: () => document.getElementById('analysisResults'),
                densityValue: () => document.getElementById('densityValue'),
                keywordOccurrences: () => document.getElementById('keywordOccurrences'),
                recommendation: () => document.getElementById('recommendation'),
                keywordList: () => document.getElementById('keywordList'),
                notification: () => document.getElementById('keywordDensityNotification')
            };

            function updateContentStats() {
                const content = elements.contentText().value;
                const words = content.trim() ? content.trim().split(/\s+/).length : 0;
                const chars = content.length;
                const paragraphs = content.trim() ? content.split(/\n\s*\n/).length : 0;

                elements.wordCount().textContent = words;
                elements.charCount().textContent = chars;
                elements.paragraphCount().textContent = paragraphs;
            }

            function cleanText(text) {
                return text.toLowerCase()
                    .replace(/[^\w\s]/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
            }

            function getWordFrequency(text) {
                const words = cleanText(text).split(' ').filter(word => word.length > 2);
                const frequency = {};

                words.forEach(word => {
                    frequency[word] = (frequency[word] || 0) + 1;
                });

                return frequency;
            }

            function calculateKeywordDensity(content, keyword) {
                if (!content.trim() || !keyword.trim()) return 0;

                const cleanContent = cleanText(content);
                const cleanKeyword = cleanText(keyword);
                const words = cleanContent.split(' ').filter(word => word.length > 0);

                if (words.length === 0) return 0;

                const keywordWords = cleanKeyword.split(' ');
                let occurrences = 0;

                if (keywordWords.length === 1) {
                    // Single word keyword
                    occurrences = words.filter(word => word === cleanKeyword).length;
                } else {
                    // Multi-word phrase
                    const contentText = cleanContent;
                    const regex = new RegExp(cleanKeyword.replace(/\s+/g, '\\s+'), 'g');
                    const matches = contentText.match(regex);
                    occurrences = matches ? matches.length : 0;
                }

                return {
                    density: ((occurrences / words.length) * 100),
                    occurrences: occurrences,
                    totalWords: words.length
                };
            }

            function getDensityStatus(density) {
                if (density === 0) return 'optimal';
                if (density >= 1 && density <= 2.5) return 'optimal';
                if (density > 2.5 && density <= 4) return 'warning';
                return 'error';
            }

            function getRecommendation(density, occurrences) {
                if (density === 0) {
                    return {
                        text: "No target keyword found in content. Consider adding your target keyword naturally throughout the text.",
                        status: 'warning'
                    };
                } else if (density >= 1 && density <= 2.5) {
                    return {
                        text: `Excellent! Your keyword density of ${density.toFixed(2)}% is within the optimal range (1-2.5%). This provides good SEO value without keyword stuffing.`,
                        status: 'optimal'
                    };
                } else if (density > 2.5 && density <= 4) {
                    return {
                        text: `Your keyword density of ${density.toFixed(2)}% is slightly high. Consider reducing keyword usage to avoid potential keyword stuffing penalties.`,
                        status: 'warning'
                    };
                } else {
                    return {
                        text: `Warning: Keyword density of ${density.toFixed(2)}% is too high and may be considered keyword stuffing. Reduce keyword usage for better SEO.`,
                        status: 'error'
                    };
                }
            }

            window.KeywordDensityChecker = {
                analyze() {
                    const keyword = elements.targetKeyword().value.trim();
                    const content = elements.contentText().value.trim();

                    if (!content) {
                        this.showNotification('Please enter content to analyze.');
                        return;
                    }

                    if (!keyword) {
                        this.showNotification('Please enter a target keyword.');
                        return;
                    }

                    const analysis = calculateKeywordDensity(content, keyword);
                    const status = getDensityStatus(analysis.density);
                    const recommendation = getRecommendation(analysis.density, analysis.occurrences);

                    // Update density display
                    const densityElement = elements.densityValue();
                    densityElement.textContent = analysis.density.toFixed(2) + '%';
                    densityElement.className = `keyword-density-checker-density-value ${status}`;

                    // Update occurrences
                    elements.keywordOccurrences().textContent = `${analysis.occurrences} occurrences found`;

                    // Update recommendation
                    const recElement = elements.recommendation();
                    recElement.textContent = recommendation.text;
                    recElement.className = `keyword-density-checker-recommendation ${recommendation.status}`;

                    // Generate top keywords list
                    this.generateKeywordList(content);

                    // Show results
                    elements.analysisResults().classList.add('show');

                    this.showNotification('✓ Analysis completed successfully!');
                },

                generateKeywordList(content) {
                    const frequency = getWordFrequency(content);
                    const sortedKeywords = Object.entries(frequency)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 10);

                    const totalWords = cleanText(content).split(' ').filter(word => word.length > 2).length;

                    let html = '';
                    if (sortedKeywords.length === 0) {
                        html = '<div style="text-align: center; color: var(--text-color-light); padding: var(--spacing-lg);">No keywords found in content.</div>';
                    } else {
                        sortedKeywords.forEach(([word, count]) => {
                            const density = ((count / totalWords) * 100).toFixed(2);
                            html += `
                                <div class="keyword-density-checker-keyword-item">
                                    <div class="keyword-density-checker-keyword-text">${word}</div>
                                    <div class="keyword-density-checker-keyword-stats">
                                        <span>${count} times</span>
                                        <span>${density}%</span>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    elements.keywordList().innerHTML = html;
                },

                clear() {
                    elements.targetKeyword().value = '';
                    elements.contentText().value = '';
                    elements.analysisResults().classList.remove('show');

                    // Reset stats
                    updateContentStats();

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Update content stats on input
                elements.contentText().addEventListener('input', updateContentStats);

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        KeywordDensityChecker.analyze();
                    }
                });

                // Initialize stats
                updateContentStats();
            });
        })();
    </script>
</body>
</html>
