<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free H1-H6 Heading Checker - Analyze HTML Heading Structure</title>
    <meta name="description" content="Check your HTML heading structure for SEO optimization. Analyze H1-H6 tags, heading hierarchy, and ensure proper heading organization for better search rankings.">
    <meta name="keywords" content="heading checker, H1 H2 H3 checker, heading structure, heading hierarchy, HTML headings, SEO headings, heading analysis">
    <link rel="canonical" href="https://www.webtoolskit.org/p/h1-h6-heading-checker.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free H1-H6 Heading Checker - Analyze HTML Heading Structure",
        "description": "Check your HTML heading structure for SEO optimization. Analyze H1-H6 tags, heading hierarchy, and ensure proper heading organization for better search rankings.",
        "url": "https://www.webtoolskit.org/p/h1-h6-heading-checker.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "H1-H6 Heading Checker",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "HTML heading analysis",
                "Heading hierarchy checking",
                "SEO heading optimization",
                "H1-H6 tag validation",
                "Content structure analysis"
            ]
        },
        "potentialAction": [
            { "@type": "CheckAction", "name": "Check Heading Structure" },
            { "@type": "AnalyzeAction", "name": "Analyze HTML Headings" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is proper heading hierarchy?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Proper heading hierarchy follows a logical structure from H1 to H6, where H1 is the main page title, H2 are major sections, H3 are subsections under H2, and so on. This structure helps search engines understand content organization and improves accessibility for screen readers."
          }
        },
        {
          "@type": "Question",
          "name": "Should I have multiple H1 tags on a page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "While HTML5 allows multiple H1 tags, SEO best practice recommends using only one H1 tag per page as the main title. This helps search engines clearly identify the primary topic and improves content hierarchy. Use H2-H6 tags for subsequent headings."
          }
        },
        {
          "@type": "Question",
          "name": "Why are heading tags important for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Heading tags help search engines understand content structure and hierarchy. They provide context about page topics, improve crawlability, and can influence rankings. Well-structured headings also enhance user experience and accessibility, making content easier to scan and navigate."
          }
        },
        {
          "@type": "Question",
          "name": "Can I skip heading levels (H1 to H3)?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "It's best practice not to skip heading levels. Follow sequential order: H1 → H2 → H3 → H4, etc. Skipping levels (like going from H1 directly to H3) can confuse search engines and screen readers about content hierarchy and structure."
          }
        },
        {
          "@type": "Question",
          "name": "How many headings should I use on a page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "There's no strict limit, but use headings purposefully to structure content logically. Typically, pages have 1 H1, several H2s for main sections, and H3-H6 as needed for subsections. Focus on creating a clear, logical hierarchy that helps users and search engines understand your content."
          }
        }
      ]
    }
    </script>

    <style>
        /* H1-H6 Heading Checker Widget - Simplified & Template Compatible */
        .heading-checker-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .heading-checker-widget-container * { box-sizing: border-box; }

        .heading-checker-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .heading-checker-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .heading-checker-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .heading-checker-field {
            display: flex;
            flex-direction: column;
        }

        .heading-checker-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .heading-checker-input,
        .heading-checker-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .heading-checker-textarea {
            resize: vertical;
            min-height: 200px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .heading-checker-input:focus,
        .heading-checker-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .heading-checker-help-text {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        .heading-checker-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .heading-checker-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .heading-checker-btn:hover { transform: translateY(-2px); }

        .heading-checker-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .heading-checker-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .heading-checker-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .heading-checker-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .heading-checker-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .heading-checker-results.show {
            display: block;
        }

        .heading-checker-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .heading-checker-score-display {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--border-color);
        }

        .heading-checker-score-value {
            font-size: 3rem;
            font-weight: 800;
            margin-right: var(--spacing-md);
        }

        .heading-checker-score-value.excellent {
            color: #10b981;
        }

        .heading-checker-score-value.good {
            color: #3b82f6;
        }

        .heading-checker-score-value.fair {
            color: #f59e0b;
        }

        .heading-checker-score-value.poor {
            color: #dc2626;
        }

        .heading-checker-score-label {
            font-size: 1.125rem;
            color: var(--text-color-light);
        }

        .heading-checker-hierarchy {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .heading-checker-hierarchy-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
        }

        .heading-checker-heading-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
        }

        .heading-checker-heading-item:last-child {
            margin-bottom: 0;
        }

        .heading-checker-heading-tag {
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: var(--spacing-md);
            min-width: 30px;
            text-align: center;
        }

        .heading-checker-heading-tag.h1 { background-color: #dc2626; color: white; }
        .heading-checker-heading-tag.h2 { background-color: #ea580c; color: white; }
        .heading-checker-heading-tag.h3 { background-color: #d97706; color: white; }
        .heading-checker-heading-tag.h4 { background-color: #ca8a04; color: white; }
        .heading-checker-heading-tag.h5 { background-color: #65a30d; color: white; }
        .heading-checker-heading-tag.h6 { background-color: #16a34a; color: white; }

        .heading-checker-heading-text {
            flex: 1;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .heading-checker-checks {
            display: grid;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .heading-checker-check-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .heading-checker-check-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .heading-checker-check-icon.pass {
            background-color: #10b981;
        }

        .heading-checker-check-icon.fail {
            background-color: #dc2626;
        }

        .heading-checker-check-icon.warning {
            background-color: #f59e0b;
        }

        .heading-checker-check-text {
            flex: 1;
            color: var(--text-color);
        }

        .heading-checker-recommendations {
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
        }

        .heading-checker-recommendations h4 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-color);
            font-size: 1rem;
            font-weight: 600;
        }

        .heading-checker-recommendation-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .heading-checker-recommendation-item {
            padding: var(--spacing-xs) 0;
            color: var(--text-color-light);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .heading-checker-recommendation-item:before {
            content: "•";
            color: var(--primary-color);
            margin-right: var(--spacing-xs);
            font-weight: bold;
        }

        .heading-checker-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .heading-checker-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .heading-checker-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .heading-checker-widget-title { font-size: 1.875rem; }
            .heading-checker-buttons { flex-direction: column; }
            .heading-checker-btn { flex: none; }
            .heading-checker-score-display { flex-direction: column; text-align: center; }
            .heading-checker-score-value { margin-right: 0; margin-bottom: var(--spacing-sm); }
        }

        [data-theme="dark"] .heading-checker-input:focus,
        [data-theme="dark"] .heading-checker-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .heading-checker-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }

        .heading-checker-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="title-meta-description-checker"] .heading-checker-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="keyword-density-checker"] .heading-checker-related-tool-icon { background: linear-gradient(145deg, #F59E0B, #D97706); }
        a[href*="alt-text-checker"] .heading-checker-related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }

        .heading-checker-related-tool-item:hover .heading-checker-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="title-meta-description-checker"]:hover .heading-checker-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }
        a[href*="keyword-density-checker"]:hover .heading-checker-related-tool-icon { background: linear-gradient(145deg, #f7ac2e, #e28417); }
        a[href*="alt-text-checker"]:hover .heading-checker-related-tool-icon { background: linear-gradient(145deg, #9373f7, #8a4ff0); }

        .heading-checker-related-tool-item { box-shadow: none; border: none; }
        .heading-checker-related-tool-item:hover { box-shadow: none; border: none; }
        .heading-checker-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .heading-checker-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .heading-checker-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .heading-checker-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .heading-checker-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .heading-checker-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .heading-checker-related-tool-item:hover .heading-checker-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .heading-checker-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .heading-checker-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .heading-checker-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .heading-checker-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .heading-checker-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .heading-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .heading-checker-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .heading-checker-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .heading-checker-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .heading-checker-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .heading-checker-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .heading-checker-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .heading-checker-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .heading-checker-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="heading-checker-widget-container">
        <h1 class="heading-checker-widget-title">H1-H6 Heading Checker</h1>
        <p class="heading-checker-widget-description">
            Analyze your HTML heading structure for SEO optimization. Check H1-H6 tags, heading hierarchy, and ensure proper content organization.
        </p>
        
        <form class="heading-checker-form">
            <div class="heading-checker-field">
                <label for="pageUrl" class="heading-checker-label">Page URL (Optional):</label>
                <input 
                    type="url" 
                    id="pageUrl" 
                    class="heading-checker-input"
                    placeholder="https://example.com/page-to-analyze"
                />
                <div class="heading-checker-help-text">
                    Enter the URL of the page you want to analyze (optional for reference).
                </div>
            </div>

            <div class="heading-checker-field">
                <label for="htmlContent" class="heading-checker-label">HTML Content or Headings:</label>
                <textarea 
                    id="htmlContent" 
                    class="heading-checker-textarea"
                    placeholder="Paste your HTML content here, or just the heading tags:&#10;&#10;<h1>Main Page Title</h1>&#10;<h2>Section Title</h2>&#10;<h3>Subsection Title</h3>&#10;<h2>Another Section</h2>"
                ></textarea>
                <div class="heading-checker-help-text">
                    Paste your HTML content or just the heading tags (H1-H6) you want to analyze.
                </div>
            </div>
        </form>

        <div class="heading-checker-buttons">
            <button class="heading-checker-btn heading-checker-btn-primary" onclick="HeadingChecker.analyze()">
                Check Headings
            </button>
            <button class="heading-checker-btn heading-checker-btn-secondary" onclick="HeadingChecker.clear()">
                Clear All
            </button>
        </div>

        <div class="heading-checker-results" id="analysisResults">
            <h3 class="heading-checker-results-title">Heading Structure Analysis</h3>

            <div class="heading-checker-score-display">
                <div class="heading-checker-score-value excellent" id="headingScore">0</div>
                <div class="heading-checker-score-label">
                    SEO Score<br>
                    <small id="scoreDescription">out of 100</small>
                </div>
            </div>

            <div class="heading-checker-hierarchy" id="headingHierarchy">
                <h4 class="heading-checker-hierarchy-title">Heading Hierarchy:</h4>
                <div id="headingList">
                    <!-- Heading list will be populated here -->
                </div>
            </div>

            <div class="heading-checker-checks" id="headingChecks">
                <!-- Heading checks will be populated here -->
            </div>

            <div class="heading-checker-recommendations" id="recommendations">
                <h4>Recommendations for Improvement:</h4>
                <ul class="heading-checker-recommendation-list" id="recommendationList">
                    <li class="heading-checker-recommendation-item">Enter HTML content to see personalized recommendations.</li>
                </ul>
            </div>
        </div>

        <div class="heading-checker-related-tools">
            <h3 class="heading-checker-related-tools-title">Related Tools</h3>
            <div class="heading-checker-related-tools-grid">
                <a href="/p/title-meta-description-checker.html" class="heading-checker-related-tool-item" rel="noopener">
                    <div class="heading-checker-related-tool-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="heading-checker-related-tool-name">Title Meta Description Checker</div>
                </a>

                <a href="/p/keyword-density-checker.html" class="heading-checker-related-tool-item" rel="noopener">
                    <div class="heading-checker-related-tool-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="heading-checker-related-tool-name">Keyword Density Checker</div>
                </a>

                <a href="/p/alt-text-checker.html" class="heading-checker-related-tool-item" rel="noopener">
                    <div class="heading-checker-related-tool-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="heading-checker-related-tool-name">Alt Text Checker</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional HTML Heading Structure Analysis</h2>
            <p>Our <strong>H1-H6 Heading Checker</strong> analyzes your HTML heading structure to ensure optimal SEO performance and accessibility. Proper heading hierarchy is crucial for search engines to understand your content organization and for screen readers to navigate your page effectively.</p>
            <p>Whether you're optimizing existing content or planning new pages, our tool provides comprehensive analysis of your heading structure, identifies issues, and offers actionable recommendations to improve both SEO rankings and user experience.</p>

            <h3>How to Use the H1-H6 Heading Checker</h3>
            <ol>
                <li><strong>Enter Page Details:</strong> Optionally add the page URL for reference and context.</li>
                <li><strong>Paste HTML Content:</strong> Add your HTML content or just the heading tags (H1-H6) you want to analyze.</li>
                <li><strong>Check Structure:</strong> Click "Check Headings" to get a comprehensive analysis of your heading hierarchy.</li>
                <li><strong>Review Results:</strong> Check your SEO score, heading hierarchy visualization, and follow recommendations for improvement.</li>
            </ol>

            <h3>Frequently Asked Questions About HTML Headings</h3>

            <h4>What is proper heading hierarchy?</h4>
            <p>Proper heading hierarchy follows a logical structure from H1 to H6, where H1 is the main page title, H2 are major sections, H3 are subsections under H2, and so on. This structure helps search engines understand content organization and improves accessibility for screen readers.</p>

            <h4>Should I have multiple H1 tags on a page?</h4>
            <p>While HTML5 allows multiple H1 tags, SEO best practice recommends using only one H1 tag per page as the main title. This helps search engines clearly identify the primary topic and improves content hierarchy. Use H2-H6 tags for subsequent headings.</p>

            <h4>Why are heading tags important for SEO?</h4>
            <p>Heading tags help search engines understand content structure and hierarchy. They provide context about page topics, improve crawlability, and can influence rankings. Well-structured headings also enhance user experience and accessibility, making content easier to scan and navigate.</p>

            <h4>Can I skip heading levels (H1 to H3)?</h4>
            <p>It's best practice not to skip heading levels. Follow sequential order: H1 → H2 → H3 → H4, etc. Skipping levels (like going from H1 directly to H3) can confuse search engines and screen readers about content hierarchy and structure.</p>

            <h4>How many headings should I use on a page?</h4>
            <p>There's no strict limit, but use headings purposefully to structure content logically. Typically, pages have 1 H1, several H2s for main sections, and H3-H6 as needed for subsections. Focus on creating a clear, logical hierarchy that helps users and search engines understand your content.</p>
        </div>

        <div class="heading-checker-features">
            <h3 class="heading-checker-features-title">Key Features:</h3>
            <ul class="heading-checker-features-list">
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">Complete Heading Structure Analysis</li>
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">SEO Hierarchy Validation</li>
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">Visual Heading Organization</li>
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">Accessibility Compliance Check</li>
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">Multiple H1 Detection</li>
                <li class="heading-checker-features-item" style="margin-bottom: 0.3em;">Hierarchy Gap Analysis</li>
                <li class="heading-checker-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="heading-checker-notification" id="headingNotification">
        ✓ Analysis completed successfully!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                pageUrl: () => document.getElementById('pageUrl'),
                htmlContent: () => document.getElementById('htmlContent'),
                analysisResults: () => document.getElementById('analysisResults'),
                headingScore: () => document.getElementById('headingScore'),
                scoreDescription: () => document.getElementById('scoreDescription'),
                headingList: () => document.getElementById('headingList'),
                headingChecks: () => document.getElementById('headingChecks'),
                recommendationList: () => document.getElementById('recommendationList'),
                notification: () => document.getElementById('headingNotification')
            };

            function extractHeadings(htmlContent) {
                // Create a temporary div to parse HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = htmlContent;

                const headings = [];
                const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];

                headingTags.forEach(tag => {
                    const elements = tempDiv.querySelectorAll(tag);
                    elements.forEach(element => {
                        headings.push({
                            tag: tag.toUpperCase(),
                            level: parseInt(tag.charAt(1)),
                            text: element.textContent.trim(),
                            html: element.outerHTML
                        });
                    });
                });

                // Sort by document order (approximate)
                return headings.sort((a, b) => {
                    const aIndex = htmlContent.indexOf(a.html);
                    const bIndex = htmlContent.indexOf(b.html);
                    return aIndex - bIndex;
                });
            }

            function analyzeHeadingStructure(headings) {
                const analysis = {
                    score: 0,
                    checks: [],
                    recommendations: []
                };

                if (headings.length === 0) {
                    analysis.checks.push({ text: 'No headings found in content', status: 'fail' });
                    analysis.recommendations.push('Add heading tags (H1-H6) to structure your content');
                    return analysis;
                }

                // Check for H1 presence and count
                const h1Count = headings.filter(h => h.level === 1).length;
                if (h1Count === 1) {
                    analysis.checks.push({ text: 'Single H1 tag found (recommended)', status: 'pass' });
                    analysis.score += 25;
                } else if (h1Count === 0) {
                    analysis.checks.push({ text: 'No H1 tag found', status: 'fail' });
                    analysis.recommendations.push('Add an H1 tag as the main page title');
                } else {
                    analysis.checks.push({ text: `Multiple H1 tags found (${h1Count})`, status: 'warning' });
                    analysis.score += 10;
                    analysis.recommendations.push('Use only one H1 tag per page for better SEO');
                }

                // Check heading hierarchy
                let hierarchyValid = true;
                let previousLevel = 0;

                for (let i = 0; i < headings.length; i++) {
                    const currentLevel = headings[i].level;

                    if (i === 0) {
                        if (currentLevel === 1) {
                            // Good start with H1
                        } else {
                            hierarchyValid = false;
                        }
                    } else {
                        // Check if we're skipping levels
                        if (currentLevel > previousLevel + 1) {
                            hierarchyValid = false;
                            break;
                        }
                    }

                    previousLevel = currentLevel;
                }

                if (hierarchyValid) {
                    analysis.checks.push({ text: 'Heading hierarchy follows logical order', status: 'pass' });
                    analysis.score += 25;
                } else {
                    analysis.checks.push({ text: 'Heading hierarchy has gaps or issues', status: 'warning' });
                    analysis.score += 10;
                    analysis.recommendations.push('Follow sequential heading order (H1 → H2 → H3, etc.) without skipping levels');
                }

                // Check for heading content quality
                const emptyHeadings = headings.filter(h => h.text.length === 0);
                if (emptyHeadings.length === 0) {
                    analysis.checks.push({ text: 'All headings have descriptive text', status: 'pass' });
                    analysis.score += 15;
                } else {
                    analysis.checks.push({ text: `${emptyHeadings.length} empty heading(s) found`, status: 'fail' });
                    analysis.recommendations.push('Add descriptive text to all heading tags');
                }

                // Check heading length
                const longHeadings = headings.filter(h => h.text.length > 70);
                if (longHeadings.length === 0) {
                    analysis.checks.push({ text: 'Heading lengths are appropriate', status: 'pass' });
                    analysis.score += 10;
                } else {
                    analysis.checks.push({ text: `${longHeadings.length} heading(s) may be too long`, status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Keep headings concise (under 70 characters) for better readability');
                }

                // Check for heading distribution
                const h2Count = headings.filter(h => h.level === 2).length;
                if (h2Count >= 2) {
                    analysis.checks.push({ text: 'Good use of H2 tags for content sections', status: 'pass' });
                    analysis.score += 15;
                } else if (h2Count === 1) {
                    analysis.checks.push({ text: 'Limited use of H2 tags', status: 'warning' });
                    analysis.score += 8;
                    analysis.recommendations.push('Consider using more H2 tags to break content into logical sections');
                } else {
                    analysis.checks.push({ text: 'No H2 tags found', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Add H2 tags to create main content sections');
                }

                // Check for excessive nesting
                const deepHeadings = headings.filter(h => h.level > 4);
                if (deepHeadings.length === 0) {
                    analysis.checks.push({ text: 'Heading structure not overly complex', status: 'pass' });
                    analysis.score += 10;
                } else {
                    analysis.checks.push({ text: 'Very deep heading nesting detected', status: 'warning' });
                    analysis.score += 5;
                    analysis.recommendations.push('Consider simplifying heading structure (avoid going deeper than H4)');
                }

                return analysis;
            }

            function getScoreClass(score) {
                if (score >= 85) return 'excellent';
                if (score >= 70) return 'good';
                if (score >= 50) return 'fair';
                return 'poor';
            }

            function getScoreDescription(score) {
                if (score >= 85) return 'Excellent structure';
                if (score >= 70) return 'Good structure';
                if (score >= 50) return 'Fair structure';
                return 'Needs improvement';
            }

            window.HeadingChecker = {
                analyze() {
                    const htmlContent = elements.htmlContent().value.trim();

                    if (!htmlContent) {
                        this.showNotification('Please enter HTML content to analyze.');
                        return;
                    }

                    const headings = extractHeadings(htmlContent);
                    const analysis = analyzeHeadingStructure(headings);

                    // Update score display
                    const scoreElement = elements.headingScore();
                    scoreElement.textContent = analysis.score;
                    scoreElement.className = `heading-checker-score-value ${getScoreClass(analysis.score)}`;
                    elements.scoreDescription().innerHTML = `${getScoreDescription(analysis.score)}<br><small>out of 100</small>`;

                    // Update heading hierarchy display
                    const headingListEl = elements.headingList();
                    if (headings.length === 0) {
                        headingListEl.innerHTML = '<div style="color: var(--text-color-light); font-style: italic;">No headings found in the provided content.</div>';
                    } else {
                        let hierarchyHTML = '';
                        headings.forEach(heading => {
                            const indent = (heading.level - 1) * 20;
                            hierarchyHTML += `
                                <div class="heading-checker-heading-item" style="margin-left: ${indent}px;">
                                    <div class="heading-checker-heading-tag ${heading.tag.toLowerCase()}">${heading.tag}</div>
                                    <div class="heading-checker-heading-text">${heading.text || '<em>Empty heading</em>'}</div>
                                </div>
                            `;
                        });
                        headingListEl.innerHTML = hierarchyHTML;
                    }

                    // Update checks
                    let checksHTML = '';
                    analysis.checks.forEach(check => {
                        const icon = check.status === 'pass' ? '✓' : check.status === 'warning' ? '!' : '✗';
                        checksHTML += `
                            <div class="heading-checker-check-item">
                                <div class="heading-checker-check-icon ${check.status}">${icon}</div>
                                <div class="heading-checker-check-text">${check.text}</div>
                            </div>
                        `;
                    });
                    elements.headingChecks().innerHTML = checksHTML;

                    // Update recommendations
                    const recommendationList = elements.recommendationList();
                    recommendationList.innerHTML = '';

                    if (analysis.recommendations.length === 0) {
                        const li = document.createElement('li');
                        li.className = 'heading-checker-recommendation-item';
                        li.textContent = 'Excellent! Your heading structure follows SEO best practices.';
                        recommendationList.appendChild(li);
                    } else {
                        analysis.recommendations.forEach(rec => {
                            const li = document.createElement('li');
                            li.className = 'heading-checker-recommendation-item';
                            li.textContent = rec;
                            recommendationList.appendChild(li);
                        });
                    }

                    // Show results
                    elements.analysisResults().classList.add('show');

                    this.showNotification('✓ Analysis completed successfully!');
                },

                clear() {
                    elements.pageUrl().value = '';
                    elements.htmlContent().value = '';
                    elements.analysisResults().classList.remove('show');

                    this.showNotification('✓ Form cleared successfully!');
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        HeadingChecker.analyze();
                    }
                });
            });
        })();
    </script>
</body>
</html>
