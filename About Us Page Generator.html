<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free About Us Page Generator - Create Professional About Pages</title>
    <meta name="description" content="Generate professional About Us pages for your website. Create compelling company stories and team introductions with our free About Us page generator tool.">
    <meta name="keywords" content="about us page generator, about page template, company about page, business about section, team page generator, company story generator">
    <link rel="canonical" href="https://www.webtoolskit.org/p/about-us-page-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free About Us Page Generator - Create Professional About Pages",
        "description": "Generate professional About Us pages for your website. Create compelling company stories and team introductions with our free About Us page generator tool.",
        "url": "https://www.webtoolskit.org/p/about-us-page-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "About Us Page Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "About Us page generation",
                "Company story creation",
                "Team introduction templates",
                "Mission statement generator",
                "Professional content creation"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate About Us Page" },
            { "@type": "CopyAction", "name": "Copy Generated Content" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What should be included in an About Us page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An effective About Us page should include your company's story and history, mission and values, team introductions, unique value proposition, achievements or milestones, and contact information. It should tell visitors who you are, what you do, and why you're different from competitors."
          }
        },
        {
          "@type": "Question",
          "name": "Why is an About Us page important for SEO?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "About Us pages are important for SEO because they build trust and credibility with both users and search engines. They provide valuable content about your business, can include relevant keywords naturally, and help establish your expertise and authority in your industry, which are important ranking factors."
          }
        },
        {
          "@type": "Question",
          "name": "How long should an About Us page be?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "An About Us page should typically be 300-800 words, providing enough detail to tell your story without overwhelming visitors. The content should be engaging, scannable with headings and bullet points, and focus on what matters most to your target audience."
          }
        },
        {
          "@type": "Question",
          "name": "What tone should I use in my About Us page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The tone should match your brand personality and target audience. It can be professional and formal for B2B companies, friendly and conversational for consumer brands, or creative and playful for artistic businesses. The key is authenticity and consistency with your overall brand voice."
          }
        },
        {
          "@type": "Question",
          "name": "Should I include team photos in my About Us page?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, including team photos humanizes your business and builds trust with visitors. Professional headshots of key team members, founders, or leadership help create personal connections. For larger teams, consider group photos or highlight key personnel who interact with customers."
          }
        }
      ]
    }
    </script>

    <style>
        /* About Us Generator Widget - Simplified & Template Compatible */
        .about-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .about-generator-widget-container * { box-sizing: border-box; }

        .about-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .about-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .about-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .about-generator-field {
            display: flex;
            flex-direction: column;
        }

        .about-generator-field-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .about-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .about-generator-input,
        .about-generator-select,
        .about-generator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .about-generator-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .about-generator-input:focus,
        .about-generator-select:focus,
        .about-generator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .about-generator-help-text {
            font-size: 0.875rem;
            color: var(--text-color-light);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        .about-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .about-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .about-generator-btn:hover { transform: translateY(-2px); }

        .about-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .about-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .about-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .about-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .about-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .about-generator-btn-success:hover {
            background-color: #059669;
        }

        .about-generator-results {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .about-generator-results.show {
            display: block;
        }

        .about-generator-results-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .about-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: 1.6;
            max-height: 500px;
            overflow-y: auto;
            color: var(--text-color);
            white-space: pre-wrap;
        }

        .about-generator-tabs {
            display: flex;
            margin-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--border-color);
        }

        .about-generator-tab {
            padding: var(--spacing-sm) var(--spacing-md);
            background: none;
            border: none;
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-color-light);
            cursor: pointer;
            transition: var(--transition-base);
            border-bottom: 2px solid transparent;
        }

        .about-generator-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .about-generator-tab:hover {
            color: var(--text-color);
        }

        .about-generator-tab-content {
            display: none;
        }

        .about-generator-tab-content.active {
            display: block;
        }

        .about-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .about-generator-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .about-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .about-generator-widget-title { font-size: 1.875rem; }
            .about-generator-buttons { flex-direction: column; }
            .about-generator-btn { flex: none; }
            .about-generator-field-row { grid-template-columns: 1fr; }
            .about-generator-tabs { flex-wrap: wrap; }
            .about-generator-tab { flex: 1; min-width: 120px; }
        }

        [data-theme="dark"] .about-generator-input:focus,
        [data-theme="dark"] .about-generator-select:focus,
        [data-theme="dark"] .about-generator-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .about-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .about-generator-output::selection { background-color: var(--primary-color); color: white; }

        .about-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="meta-tag-generator"] .about-generator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="terms-of-use-page-generator"] .about-generator-related-tool-icon { background: linear-gradient(145deg, #DC2626, #B91C1C); }
        a[href*="contact-form-generator"] .about-generator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .about-generator-related-tool-item:hover .about-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="meta-tag-generator"]:hover .about-generator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="terms-of-use-page-generator"]:hover .about-generator-related-tool-icon { background: linear-gradient(145deg, #ef4444, #dc2626); }
        a[href*="contact-form-generator"]:hover .about-generator-related-tool-icon { background: linear-gradient(145deg, #38d9a9, #20c997); }

        .about-generator-related-tool-item { box-shadow: none; border: none; }
        .about-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .about-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .about-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .about-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .about-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .about-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .about-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .about-generator-related-tool-item:hover .about-generator-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .about-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .about-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .about-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .about-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .about-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .about-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .about-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .about-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .about-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .about-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .about-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .about-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .about-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .about-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="about-generator-widget-container">
        <h1 class="about-generator-widget-title">About Us Page Generator</h1>
        <p class="about-generator-widget-description">
            Generate professional About Us pages for your website. Create compelling company stories and team introductions that build trust and connect with your audience.
        </p>
        
        <form class="about-generator-form">
            <div class="about-generator-field-row">
                <div class="about-generator-field">
                    <label for="companyName" class="about-generator-label">Company Name:</label>
                    <input 
                        type="text" 
                        id="companyName" 
                        class="about-generator-input"
                        placeholder="Your Company Name"
                        required
                    />
                </div>
                <div class="about-generator-field">
                    <label for="foundedYear" class="about-generator-label">Founded Year:</label>
                    <input 
                        type="number" 
                        id="foundedYear" 
                        class="about-generator-input"
                        placeholder="2020"
                        min="1800"
                        max="2025"
                    />
                </div>
            </div>

            <div class="about-generator-field-row">
                <div class="about-generator-field">
                    <label for="industry" class="about-generator-label">Industry:</label>
                    <select id="industry" class="about-generator-select">
                        <option value="">Select Industry</option>
                        <option value="technology">Technology</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="education">Education</option>
                        <option value="retail">Retail</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="consulting">Consulting</option>
                        <option value="marketing">Marketing</option>
                        <option value="real-estate">Real Estate</option>
                        <option value="food-beverage">Food & Beverage</option>
                        <option value="entertainment">Entertainment</option>
                        <option value="non-profit">Non-Profit</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="about-generator-field">
                    <label for="location" class="about-generator-label">Location:</label>
                    <input 
                        type="text" 
                        id="location" 
                        class="about-generator-input"
                        placeholder="City, State/Country"
                    />
                </div>
            </div>

            <div class="about-generator-field">
                <label for="companyStory" class="about-generator-label">Company Story/Background:</label>
                <textarea 
                    id="companyStory" 
                    class="about-generator-textarea"
                    placeholder="Tell us about how your company started, what inspired you to begin this journey, and key milestones in your growth..."
                ></textarea>
                <div class="about-generator-help-text">
                    Share your origin story, founding inspiration, and key moments in your company's journey.
                </div>
            </div>

            <div class="about-generator-field">
                <label for="mission" class="about-generator-label">Mission Statement:</label>
                <textarea 
                    id="mission" 
                    class="about-generator-textarea"
                    placeholder="What is your company's mission? What do you aim to achieve for your customers and the world?"
                ></textarea>
                <div class="about-generator-help-text">
                    Describe your company's purpose and what you're working to accomplish.
                </div>
            </div>

            <div class="about-generator-field">
                <label for="values" class="about-generator-label">Core Values:</label>
                <textarea 
                    id="values" 
                    class="about-generator-textarea"
                    placeholder="What principles guide your business? (e.g., Innovation, Integrity, Customer Focus, Quality, Sustainability)"
                ></textarea>
                <div class="about-generator-help-text">
                    List the key values and principles that drive your business decisions.
                </div>
            </div>

            <div class="about-generator-field">
                <label for="achievements" class="about-generator-label">Key Achievements/Milestones:</label>
                <textarea 
                    id="achievements" 
                    class="about-generator-textarea"
                    placeholder="Notable accomplishments, awards, certifications, growth milestones, or recognition your company has received..."
                ></textarea>
                <div class="about-generator-help-text">
                    Highlight important achievements that build credibility and trust.
                </div>
            </div>

            <div class="about-generator-field">
                <label for="teamInfo" class="about-generator-label">Team Information:</label>
                <textarea 
                    id="teamInfo" 
                    class="about-generator-textarea"
                    placeholder="Information about your team, founders, key personnel, company culture, or what makes your team special..."
                ></textarea>
                <div class="about-generator-help-text">
                    Describe your team, leadership, and what makes your people unique.
                </div>
            </div>
        </form>

        <div class="about-generator-buttons">
            <button class="about-generator-btn about-generator-btn-primary" onclick="AboutGenerator.generate()">
                Generate About Us Page
            </button>
            <button class="about-generator-btn about-generator-btn-secondary" onclick="AboutGenerator.clear()">
                Clear All
            </button>
            <button class="about-generator-btn about-generator-btn-success" onclick="AboutGenerator.copy()">
                Copy Content
            </button>
        </div>

        <div class="about-generator-results" id="generationResults">
            <h3 class="about-generator-results-title">Generated About Us Page</h3>

            <div class="about-generator-tabs">
                <button class="about-generator-tab active" onclick="AboutGenerator.switchTab('html')">HTML Version</button>
                <button class="about-generator-tab" onclick="AboutGenerator.switchTab('text')">Text Version</button>
            </div>

            <div class="about-generator-tab-content active" id="tab-html">
                <div class="about-generator-output" id="htmlOutput">
                    Your generated HTML About Us page will appear here...
                </div>
            </div>

            <div class="about-generator-tab-content" id="tab-text">
                <div class="about-generator-output" id="textOutput">
                    Your generated text About Us content will appear here...
                </div>
            </div>
        </div>

        <div class="about-generator-related-tools">
            <h3 class="about-generator-related-tools-title">Related Tools</h3>
            <div class="about-generator-related-tools-grid">
                <a href="/p/meta-tag-generator.html" class="about-generator-related-tool-item" rel="noopener">
                    <div class="about-generator-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="about-generator-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/terms-of-use-page-generator.html" class="about-generator-related-tool-item" rel="noopener">
                    <div class="about-generator-related-tool-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <div class="about-generator-related-tool-name">Terms of Use Generator</div>
                </a>

                <a href="/p/contact-form-generator.html" class="about-generator-related-tool-item" rel="noopener">
                    <div class="about-generator-related-tool-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="about-generator-related-tool-name">Contact Form Generator</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional About Us Page Generator for Websites</h2>
            <p>Our <strong>About Us Page Generator</strong> helps you create compelling and professional About Us pages that build trust, establish credibility, and connect with your audience. A well-crafted About Us page is one of the most visited pages on any website and plays a crucial role in converting visitors into customers.</p>
            <p>Whether you're a startup, established business, or personal brand, our tool generates both HTML and text versions of your About Us content, making it easy to implement on any website platform or content management system.</p>

            <h3>How to Use the About Us Page Generator</h3>
            <ol>
                <li><strong>Enter Company Details:</strong> Provide your company name, founding year, industry, and location.</li>
                <li><strong>Share Your Story:</strong> Tell us about your company's background, inspiration, and journey.</li>
                <li><strong>Define Your Purpose:</strong> Add your mission statement and core values that guide your business.</li>
                <li><strong>Highlight Achievements:</strong> Include key milestones, awards, and accomplishments.</li>
                <li><strong>Introduce Your Team:</strong> Share information about your team, culture, and what makes you unique.</li>
                <li><strong>Generate & Implement:</strong> Create your About Us page and copy the HTML or text version to your website.</li>
            </ol>

            <h3>Frequently Asked Questions About About Us Pages</h3>

            <h4>What should be included in an About Us page?</h4>
            <p>An effective About Us page should include your company's story and history, mission and values, team introductions, unique value proposition, achievements or milestones, and contact information. It should tell visitors who you are, what you do, and why you're different from competitors.</p>

            <h4>Why is an About Us page important for SEO?</h4>
            <p>About Us pages are important for SEO because they build trust and credibility with both users and search engines. They provide valuable content about your business, can include relevant keywords naturally, and help establish your expertise and authority in your industry, which are important ranking factors.</p>

            <h4>How long should an About Us page be?</h4>
            <p>An About Us page should typically be 300-800 words, providing enough detail to tell your story without overwhelming visitors. The content should be engaging, scannable with headings and bullet points, and focus on what matters most to your target audience.</p>

            <h4>What tone should I use in my About Us page?</h4>
            <p>The tone should match your brand personality and target audience. It can be professional and formal for B2B companies, friendly and conversational for consumer brands, or creative and playful for artistic businesses. The key is authenticity and consistency with your overall brand voice.</p>

            <h4>Should I include team photos in my About Us page?</h4>
            <p>Yes, including team photos humanizes your business and builds trust with visitors. Professional headshots of key team members, founders, or leadership help create personal connections. For larger teams, consider group photos or highlight key personnel who interact with customers.</p>
        </div>

        <div class="about-generator-features">
            <h3 class="about-generator-features-title">Key Features:</h3>
            <ul class="about-generator-features-list">
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">Professional Content Templates</li>
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">HTML & Text Output Formats</li>
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">Industry-Specific Customization</li>
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">SEO-Optimized Structure</li>
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">Engaging Storytelling Format</li>
                <li class="about-generator-features-item" style="margin-bottom: 0.3em;">Easy Copy & Implementation</li>
                <li class="about-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="about-generator-notification" id="aboutNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                companyName: () => document.getElementById('companyName'),
                foundedYear: () => document.getElementById('foundedYear'),
                industry: () => document.getElementById('industry'),
                location: () => document.getElementById('location'),
                companyStory: () => document.getElementById('companyStory'),
                mission: () => document.getElementById('mission'),
                values: () => document.getElementById('values'),
                achievements: () => document.getElementById('achievements'),
                teamInfo: () => document.getElementById('teamInfo'),
                generationResults: () => document.getElementById('generationResults'),
                htmlOutput: () => document.getElementById('htmlOutput'),
                textOutput: () => document.getElementById('textOutput'),
                notification: () => document.getElementById('aboutNotification')
            };

            let currentTab = 'html';

            function generateAboutUsContent(data) {
                const currentYear = new Date().getFullYear();
                const yearsInBusiness = data.foundedYear ? currentYear - parseInt(data.foundedYear) : null;

                // Generate HTML version
                let htmlContent = `<div class="about-us-page">
    <h1>About ${data.companyName}</h1>

    ${data.companyStory ? `<section class="company-story">
        <h2>Our Story</h2>
        <p>${data.companyStory}</p>
        ${data.foundedYear ? `<p>Founded in ${data.foundedYear}${yearsInBusiness ? `, we have been serving our customers for over ${yearsInBusiness} years` : ''}, ${data.location ? `based in ${data.location}` : 'and'} ${data.industry ? `specializing in the ${data.industry} industry` : 'committed to excellence in everything we do'}.</p>` : ''}
    </section>` : ''}

    ${data.mission ? `<section class="mission-statement">
        <h2>Our Mission</h2>
        <p>${data.mission}</p>
    </section>` : ''}

    ${data.values ? `<section class="core-values">
        <h2>Our Values</h2>
        <p>${data.values}</p>
    </section>` : ''}

    ${data.achievements ? `<section class="achievements">
        <h2>Our Achievements</h2>
        <p>${data.achievements}</p>
    </section>` : ''}

    ${data.teamInfo ? `<section class="our-team">
        <h2>Our Team</h2>
        <p>${data.teamInfo}</p>
    </section>` : ''}

    <section class="contact-info">
        <h2>Get in Touch</h2>
        <p>Ready to learn more about how ${data.companyName} can help you? We'd love to hear from you. Contact us today to discuss your needs and discover how our ${data.industry ? `${data.industry} ` : ''}expertise can benefit your business.</p>
    </section>
</div>`;

                // Generate text version
                let textContent = `About ${data.companyName}

${data.companyStory ? `Our Story

${data.companyStory}

${data.foundedYear ? `Founded in ${data.foundedYear}${yearsInBusiness ? `, we have been serving our customers for over ${yearsInBusiness} years` : ''}, ${data.location ? `based in ${data.location}` : 'and'} ${data.industry ? `specializing in the ${data.industry} industry` : 'committed to excellence in everything we do'}.

` : ''}` : ''}${data.mission ? `Our Mission

${data.mission}

` : ''}${data.values ? `Our Values

${data.values}

` : ''}${data.achievements ? `Our Achievements

${data.achievements}

` : ''}${data.teamInfo ? `Our Team

${data.teamInfo}

` : ''}Get in Touch

Ready to learn more about how ${data.companyName} can help you? We'd love to hear from you. Contact us today to discuss your needs and discover how our ${data.industry ? `${data.industry} ` : ''}expertise can benefit your business.`;

                return { html: htmlContent, text: textContent };
            }

            window.AboutGenerator = {
                generate() {
                    const companyName = elements.companyName().value.trim();

                    if (!companyName) {
                        this.showNotification('Please enter at least a company name.');
                        return;
                    }

                    const data = {
                        companyName,
                        foundedYear: elements.foundedYear().value.trim(),
                        industry: elements.industry().value,
                        location: elements.location().value.trim(),
                        companyStory: elements.companyStory().value.trim(),
                        mission: elements.mission().value.trim(),
                        values: elements.values().value.trim(),
                        achievements: elements.achievements().value.trim(),
                        teamInfo: elements.teamInfo().value.trim()
                    };

                    const generatedContent = generateAboutUsContent(data);

                    elements.htmlOutput().innerHTML = generatedContent.html;
                    elements.textOutput().textContent = generatedContent.text;
                    elements.generationResults().classList.add('show');

                    this.showNotification('✓ About Us page generated successfully!');
                },

                switchTab(tabName) {
                    currentTab = tabName;

                    // Remove active class from all tabs and content
                    document.querySelectorAll('.about-generator-tab').forEach(tab => tab.classList.remove('active'));
                    document.querySelectorAll('.about-generator-tab-content').forEach(content => content.classList.remove('active'));

                    // Add active class to selected tab and content
                    event.target.classList.add('active');
                    document.getElementById(`tab-${tabName}`).classList.add('active');
                },

                copy() {
                    let content;
                    if (currentTab === 'html') {
                        content = elements.htmlOutput().innerHTML;
                        if (content === 'Your generated HTML About Us page will appear here...') {
                            this.showNotification('Please generate About Us content first.');
                            return;
                        }
                    } else {
                        content = elements.textOutput().textContent;
                        if (content === 'Your generated text About Us content will appear here...') {
                            this.showNotification('Please generate About Us content first.');
                            return;
                        }
                    }

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(content).then(() => {
                            this.showNotification(`✓ ${currentTab.toUpperCase()} content copied to clipboard!`);
                        }).catch(() => {
                            this.fallbackCopy(content);
                        });
                    } else {
                        this.fallbackCopy(content);
                    }
                },

                clear() {
                    elements.companyName().value = '';
                    elements.foundedYear().value = '';
                    elements.industry().selectedIndex = 0;
                    elements.location().value = '';
                    elements.companyStory().value = '';
                    elements.mission().value = '';
                    elements.values().value = '';
                    elements.achievements().value = '';
                    elements.teamInfo().value = '';
                    elements.generationResults().classList.remove('show');

                    this.showNotification('✓ Form cleared successfully!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification(`✓ ${currentTab.toUpperCase()} content copied to clipboard!`);
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                        this.showNotification('Copy failed. Please select and copy manually.');
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message) {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        AboutGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
