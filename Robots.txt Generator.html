<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Robots.txt Generator - Create SEO-Friendly Robots.txt Files</title>
    <meta name="description" content="Generate professional robots.txt files instantly with our free Robots.txt Generator. Control search engine crawling, improve SEO, and protect your website content.">
    <meta name="keywords" content="robots.txt generator, robots txt, robots.txt file, seo robots.txt, user-agent, disallow, allow, sitemap">
    <link rel="canonical" href="https://www.webtoolskit.org/p/robots-txt-generator.html">

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "WebApplication"],
        "name": "Free Robots.txt Generator - Create SEO-Friendly Robots.txt Files",
        "description": "Generate professional robots.txt files instantly with our free Robots.txt Generator. Control search engine crawling, improve SEO, and protect your website content.",
        "url": "https://www.webtoolskit.org/p/robots-txt-generator.html",
        "isAccessibleForFree": true,
        "datePublished": "2025-06-27",
        "dateModified": "2025-06-27",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Robots.txt Generator",
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" },
            "featureList": [
                "Robots.txt file generation",
                "User-agent configuration",
                "Allow/Disallow rules",
                "Sitemap inclusion",
                "SEO optimization"
            ]
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Robots.txt" },
            { "@type": "DownloadAction", "name": "Download Robots.txt File" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a robots.txt file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A robots.txt file is a text file placed in your website's root directory that tells search engine crawlers which pages or sections of your site they should or shouldn't visit. It's part of the Robots Exclusion Protocol and helps you control how search engines crawl and index your website."
          }
        },
        {
          "@type": "Question",
          "name": "How do I create a robots.txt file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using our Robots.txt Generator is the easiest way. Simply select your preferences for different search engines, specify which directories to allow or disallow, add your sitemap URL, and click 'Generate Robots.txt'. The tool will create a properly formatted file that you can download and upload to your website's root directory."
          }
        },
        {
          "@type": "Question",
          "name": "Where should I place my robots.txt file?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Your robots.txt file must be placed in the root directory of your website, accessible at yourdomain.com/robots.txt. It cannot be placed in subdirectories or subdomains. The file must be named exactly 'robots.txt' (lowercase) and be publicly accessible."
          }
        },
        {
          "@type": "Question",
          "name": "What does 'User-agent: *' mean in robots.txt?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "'User-agent: *' means the rules apply to all web crawlers and search engine bots. The asterisk (*) is a wildcard that targets every crawler. You can also specify individual crawlers like 'User-agent: Googlebot' to create rules for specific search engines."
          }
        },
        {
          "@type": "Question",
          "name": "What's the difference between 'Allow' and 'Disallow' in robots.txt?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "'Disallow' tells crawlers not to access specific pages or directories, while 'Allow' explicitly permits access to pages or directories. 'Disallow: /' blocks the entire site, while 'Disallow:' (empty) allows everything. 'Allow' is useful for permitting access to specific files within a disallowed directory."
          }
        }
      ]
    }
    </script>

    <style>
        /* Robots.txt Generator Widget - Simplified & Template Compatible */
        .robots-txt-generator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .robots-txt-generator-widget-container * { box-sizing: border-box; }

        .robots-txt-generator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .robots-txt-generator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .robots-txt-generator-form {
            display: grid;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .robots-txt-generator-section {
            background-color: var(--background-color-alt);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .robots-txt-generator-section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .robots-txt-generator-field {
            display: flex;
            flex-direction: column;
            margin-bottom: var(--spacing-md);
        }

        .robots-txt-generator-field:last-child {
            margin-bottom: 0;
        }

        .robots-txt-generator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .robots-txt-generator-input,
        .robots-txt-generator-textarea,
        .robots-txt-generator-select {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--card-bg);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .robots-txt-generator-textarea {
            resize: vertical;
            min-height: 100px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .robots-txt-generator-input:focus,
        .robots-txt-generator-textarea:focus,
        .robots-txt-generator-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .robots-txt-generator-checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
        }

        .robots-txt-generator-checkbox-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            transition: var(--transition-base);
        }

        .robots-txt-generator-checkbox-item:hover {
            background-color: var(--border-color);
        }

        .robots-txt-generator-checkbox {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .robots-txt-generator-checkbox-label {
            font-size: 0.9rem;
            color: var(--text-color);
            cursor: pointer;
        }

        .robots-txt-generator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .robots-txt-generator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .robots-txt-generator-btn:hover { transform: translateY(-2px); }

        .robots-txt-generator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .robots-txt-generator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .robots-txt-generator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .robots-txt-generator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .robots-txt-generator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .robots-txt-generator-btn-success:hover {
            background-color: #059669;
        }

        .robots-txt-generator-btn-download {
            background-color: #8B5CF6;
            color: white;
        }

        .robots-txt-generator-btn-download:hover {
            background-color: #7C3AED;
        }

        .robots-txt-generator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .robots-txt-generator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .robots-txt-generator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 200px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .robots-txt-generator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .robots-txt-generator-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .robots-txt-generator-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .robots-txt-generator-widget-title { font-size: 1.875rem; }
            .robots-txt-generator-buttons { flex-direction: column; }
            .robots-txt-generator-btn { flex: none; }
            .robots-txt-generator-checkbox-group { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .robots-txt-generator-input:focus,
        [data-theme="dark"] .robots-txt-generator-textarea:focus,
        [data-theme="dark"] .robots-txt-generator-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .robots-txt-generator-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .robots-txt-generator-output::selection { background-color: var(--primary-color); color: white; }

        .robots-txt-generator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="sitemap-generator"] .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="meta-tag-generator"] .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="url-seo-analyzer"] .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #6366F1, #4F46E5); }

        .robots-txt-generator-related-tool-item:hover .robots-txt-generator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="sitemap-generator"]:hover .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #f05eab, #e43887); }
        a[href*="meta-tag-generator"]:hover .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="url-seo-analyzer"]:hover .robots-txt-generator-related-tool-icon { background: linear-gradient(145deg, #7c3aed, #6366f1); }

        .robots-txt-generator-related-tool-item { box-shadow: none; border: none; }
        .robots-txt-generator-related-tool-item:hover { box-shadow: none; border: none; }
        .robots-txt-generator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .robots-txt-generator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .robots-txt-generator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .robots-txt-generator-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .robots-txt-generator-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .robots-txt-generator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .robots-txt-generator-related-tool-item:hover .robots-txt-generator-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .robots-txt-generator-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .robots-txt-generator-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .robots-txt-generator-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }

        .robots-txt-generator-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .robots-txt-generator-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        @media (max-width: 768px) {
            .robots-txt-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .robots-txt-generator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .robots-txt-generator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .robots-txt-generator-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { .robots-txt-generator-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }

        @media (max-width: 480px) {
            .robots-txt-generator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .robots-txt-generator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .robots-txt-generator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .robots-txt-generator-related-tool-name { font-size: 0.75rem; }
        }
    </style>
</head>
<body>
    <div class="robots-txt-generator-widget-container">
        <h1 class="robots-txt-generator-widget-title">Robots.txt Generator</h1>
        <p class="robots-txt-generator-widget-description">
            Create professional robots.txt files to control how search engines crawl your website. Improve your SEO and protect sensitive content with our easy-to-use generator.
        </p>
        
        <form class="robots-txt-generator-form">
            <div class="robots-txt-generator-section">
                <h3 class="robots-txt-generator-section-title">
                    <i class="fas fa-robot"></i>
                    Search Engine Access
                </h3>
                <div class="robots-txt-generator-field">
                    <label class="robots-txt-generator-label">Allow search engines to crawl your website:</label>
                    <div class="robots-txt-generator-checkbox-group">
                        <div class="robots-txt-generator-checkbox-item">
                            <input type="radio" id="allowAll" name="crawlAccess" value="allow" class="robots-txt-generator-checkbox" checked>
                            <label for="allowAll" class="robots-txt-generator-checkbox-label">Allow all search engines</label>
                        </div>
                        <div class="robots-txt-generator-checkbox-item">
                            <input type="radio" id="blockAll" name="crawlAccess" value="block" class="robots-txt-generator-checkbox">
                            <label for="blockAll" class="robots-txt-generator-checkbox-label">Block all search engines</label>
                        </div>
                        <div class="robots-txt-generator-checkbox-item">
                            <input type="radio" id="custom" name="crawlAccess" value="custom" class="robots-txt-generator-checkbox">
                            <label for="custom" class="robots-txt-generator-checkbox-label">Custom configuration</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="robots-txt-generator-section" id="customSection" style="display: none;">
                <h3 class="robots-txt-generator-section-title">
                    <i class="fas fa-cog"></i>
                    Custom Rules
                </h3>
                <div class="robots-txt-generator-field">
                    <label for="userAgent" class="robots-txt-generator-label">User-Agent (leave blank for all):</label>
                    <input
                        type="text"
                        id="userAgent"
                        class="robots-txt-generator-input"
                        placeholder="e.g., Googlebot, Bingbot, or * for all"
                    />
                </div>
                <div class="robots-txt-generator-field">
                    <label for="disallowPaths" class="robots-txt-generator-label">Disallow Paths (one per line):</label>
                    <textarea
                        id="disallowPaths"
                        class="robots-txt-generator-textarea"
                        placeholder="e.g.,&#10;/admin/&#10;/private/&#10;/wp-admin/"
                    ></textarea>
                </div>
                <div class="robots-txt-generator-field">
                    <label for="allowPaths" class="robots-txt-generator-label">Allow Paths (one per line):</label>
                    <textarea
                        id="allowPaths"
                        class="robots-txt-generator-textarea"
                        placeholder="e.g.,&#10;/public/&#10;/images/"
                    ></textarea>
                </div>
            </div>

            <div class="robots-txt-generator-section">
                <h3 class="robots-txt-generator-section-title">
                    <i class="fas fa-sitemap"></i>
                    Sitemap Configuration
                </h3>
                <div class="robots-txt-generator-field">
                    <label for="sitemapUrl" class="robots-txt-generator-label">Sitemap URL:</label>
                    <input
                        type="url"
                        id="sitemapUrl"
                        class="robots-txt-generator-input"
                        placeholder="https://example.com/sitemap.xml"
                    />
                </div>
            </div>

            <div class="robots-txt-generator-section">
                <h3 class="robots-txt-generator-section-title">
                    <i class="fas fa-clock"></i>
                    Crawl Delay (Optional)
                </h3>
                <div class="robots-txt-generator-field">
                    <label for="crawlDelay" class="robots-txt-generator-label">Crawl Delay (seconds):</label>
                    <input
                        type="number"
                        id="crawlDelay"
                        class="robots-txt-generator-input"
                        placeholder="10"
                        min="0"
                        max="86400"
                    />
                    <small style="color: var(--text-color-light); margin-top: var(--spacing-xs);">
                        Delay between requests (0-86400 seconds). Use with caution as it may slow down indexing.
                    </small>
                </div>
            </div>
        </form>

        <div class="robots-txt-generator-buttons">
            <button class="robots-txt-generator-btn robots-txt-generator-btn-primary" onclick="RobotsTxtGenerator.generate()">
                Generate Robots.txt
            </button>
            <button class="robots-txt-generator-btn robots-txt-generator-btn-secondary" onclick="RobotsTxtGenerator.clear()">
                Clear All
            </button>
            <button class="robots-txt-generator-btn robots-txt-generator-btn-success" onclick="RobotsTxtGenerator.copy()">
                Copy Content
            </button>
            <button class="robots-txt-generator-btn robots-txt-generator-btn-download" onclick="RobotsTxtGenerator.download()">
                Download File
            </button>
        </div>

        <div class="robots-txt-generator-result">
            <h3 class="robots-txt-generator-result-title">Generated Robots.txt:</h3>
            <div class="robots-txt-generator-output" id="robotsTxtOutput">Your robots.txt file content will appear here...</div>
        </div>

        <div class="robots-txt-generator-related-tools">
            <h3 class="robots-txt-generator-related-tools-title">Related Tools</h3>
            <div class="robots-txt-generator-related-tools-grid">
                <a href="/p/sitemap-generator.html" class="robots-txt-generator-related-tool-item" rel="noopener">
                    <div class="robots-txt-generator-related-tool-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="robots-txt-generator-related-tool-name">Sitemap Generator</div>
                </a>

                <a href="/p/meta-tag-generator.html" class="robots-txt-generator-related-tool-item" rel="noopener">
                    <div class="robots-txt-generator-related-tool-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="robots-txt-generator-related-tool-name">Meta Tag Generator</div>
                </a>

                <a href="/p/url-seo-analyzer.html" class="robots-txt-generator-related-tool-item" rel="noopener">
                    <div class="robots-txt-generator-related-tool-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="robots-txt-generator-related-tool-name">URL SEO Analyzer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Robots.txt Generator for Better SEO Control</h2>
            <p>Our <strong>Robots.txt Generator</strong> helps you create properly formatted robots.txt files that give you complete control over how search engines crawl and index your website. A well-configured robots.txt file is essential for SEO optimization, allowing you to guide search engine bots to your most important content while protecting sensitive areas of your site.</p>
            <p>Whether you're a website owner, SEO professional, or developer, our tool makes it easy to generate compliant robots.txt files that follow industry best practices. Simply configure your preferences, and we'll create a professional file ready for upload to your website's root directory.</p>

            <h3>How to Use the Robots.txt Generator</h3>
            <ol>
                <li><strong>Choose Access Level:</strong> Select whether to allow all search engines, block all, or create custom rules.</li>
                <li><strong>Configure Custom Rules:</strong> If using custom settings, specify user-agents, disallow/allow paths, and crawl delays.</li>
                <li><strong>Add Sitemap URL:</strong> Include your sitemap location to help search engines discover your content.</li>
                <li><strong>Generate and Download:</strong> Click "Generate Robots.txt" and download the file to upload to your website's root directory.</li>
            </ol>

            <h3>Frequently Asked Questions About Robots.txt</h3>

            <h4>What is a robots.txt file?</h4>
            <p>A robots.txt file is a text file placed in your website's root directory that tells search engine crawlers which pages or sections of your site they should or shouldn't visit. It's part of the Robots Exclusion Protocol and helps you control how search engines crawl and index your website.</p>

            <h4>How do I create a robots.txt file?</h4>
            <p>Using our Robots.txt Generator is the easiest way. Simply select your preferences for different search engines, specify which directories to allow or disallow, add your sitemap URL, and click 'Generate Robots.txt'. The tool will create a properly formatted file that you can download and upload to your website's root directory.</p>

            <h4>Where should I place my robots.txt file?</h4>
            <p>Your robots.txt file must be placed in the root directory of your website, accessible at yourdomain.com/robots.txt. It cannot be placed in subdirectories or subdomains. The file must be named exactly 'robots.txt' (lowercase) and be publicly accessible.</p>

            <h4>What does 'User-agent: *' mean in robots.txt?</h4>
            <p>'User-agent: *' means the rules apply to all web crawlers and search engine bots. The asterisk (*) is a wildcard that targets every crawler. You can also specify individual crawlers like 'User-agent: Googlebot' to create rules for specific search engines.</p>

            <h4>What's the difference between 'Allow' and 'Disallow' in robots.txt?</h4>
            <p>'Disallow' tells crawlers not to access specific pages or directories, while 'Allow' explicitly permits access to pages or directories. 'Disallow: /' blocks the entire site, while 'Disallow:' (empty) allows everything. 'Allow' is useful for permitting access to specific files within a disallowed directory.</p>
        </div>

        <div class="robots-txt-generator-features">
            <h3 class="robots-txt-generator-features-title">Key Features:</h3>
            <ul class="robots-txt-generator-features-list">
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">Professional Robots.txt Generation</li>
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">Custom User-Agent Rules</li>
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">Allow/Disallow Path Configuration</li>
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">Sitemap URL Integration</li>
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">Crawl Delay Settings</li>
                <li class="robots-txt-generator-features-item" style="margin-bottom: 0.3em;">One-Click Download</li>
                <li class="robots-txt-generator-features-item">100% Free and Secure</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="robots-txt-generator-notification" id="robotsTxtNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        (function() {
            'use strict';

            const elements = {
                crawlAccess: () => document.querySelector('input[name="crawlAccess"]:checked'),
                customSection: () => document.getElementById('customSection'),
                userAgent: () => document.getElementById('userAgent'),
                disallowPaths: () => document.getElementById('disallowPaths'),
                allowPaths: () => document.getElementById('allowPaths'),
                sitemapUrl: () => document.getElementById('sitemapUrl'),
                crawlDelay: () => document.getElementById('crawlDelay'),
                output: () => document.getElementById('robotsTxtOutput'),
                notification: () => document.getElementById('robotsTxtNotification')
            };

            window.RobotsTxtGenerator = {
                generate() {
                    const crawlAccess = elements.crawlAccess().value;
                    const userAgent = elements.userAgent().value.trim() || '*';
                    const disallowPaths = elements.disallowPaths().value.trim();
                    const allowPaths = elements.allowPaths().value.trim();
                    const sitemapUrl = elements.sitemapUrl().value.trim();
                    const crawlDelay = elements.crawlDelay().value.trim();
                    const output = elements.output();

                    let robotsTxt = '';

                    // Generate based on access level
                    if (crawlAccess === 'allow') {
                        robotsTxt += 'User-agent: *\n';
                        robotsTxt += 'Disallow:\n\n';
                    } else if (crawlAccess === 'block') {
                        robotsTxt += 'User-agent: *\n';
                        robotsTxt += 'Disallow: /\n\n';
                    } else if (crawlAccess === 'custom') {
                        robotsTxt += `User-agent: ${userAgent}\n`;

                        // Add disallow paths
                        if (disallowPaths) {
                            const disallowLines = disallowPaths.split('\n').filter(line => line.trim());
                            disallowLines.forEach(path => {
                                const cleanPath = path.trim();
                                if (cleanPath && !cleanPath.startsWith('/')) {
                                    robotsTxt += `Disallow: /${cleanPath}\n`;
                                } else if (cleanPath) {
                                    robotsTxt += `Disallow: ${cleanPath}\n`;
                                }
                            });
                        }

                        // Add allow paths
                        if (allowPaths) {
                            const allowLines = allowPaths.split('\n').filter(line => line.trim());
                            allowLines.forEach(path => {
                                const cleanPath = path.trim();
                                if (cleanPath && !cleanPath.startsWith('/')) {
                                    robotsTxt += `Allow: /${cleanPath}\n`;
                                } else if (cleanPath) {
                                    robotsTxt += `Allow: ${cleanPath}\n`;
                                }
                            });
                        }

                        // Add crawl delay if specified
                        if (crawlDelay && parseInt(crawlDelay) > 0) {
                            robotsTxt += `Crawl-delay: ${crawlDelay}\n`;
                        }

                        robotsTxt += '\n';
                    }

                    // Add sitemap if provided
                    if (sitemapUrl) {
                        robotsTxt += `Sitemap: ${sitemapUrl}\n`;
                    }

                    // If no content generated, show default
                    if (!robotsTxt.trim()) {
                        robotsTxt = 'User-agent: *\nDisallow:\n\n';
                    }

                    output.textContent = robotsTxt;
                    output.style.color = '';
                },

                clear() {
                    // Reset radio buttons
                    document.getElementById('allowAll').checked = true;
                    elements.customSection().style.display = 'none';

                    // Clear all inputs
                    elements.userAgent().value = '';
                    elements.disallowPaths().value = '';
                    elements.allowPaths().value = '';
                    elements.sitemapUrl().value = '';
                    elements.crawlDelay().value = '';
                    elements.output().textContent = 'Your robots.txt file content will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (text === 'Your robots.txt file content will appear here...' || text.startsWith('Error:')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                download() {
                    const text = elements.output().textContent;
                    if (text === 'Your robots.txt file content will appear here...' || text.startsWith('Error:')) {
                        alert('Please generate robots.txt content first.');
                        return;
                    }

                    const blob = new Blob([text], { type: 'text/plain' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'robots.txt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    this.showNotification('File downloaded successfully!');
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification(message = '✓ Copied to clipboard!') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                // Handle radio button changes
                const radioButtons = document.querySelectorAll('input[name="crawlAccess"]');
                radioButtons.forEach(radio => {
                    radio.addEventListener('change', function() {
                        const customSection = elements.customSection();
                        if (this.value === 'custom') {
                            customSection.style.display = 'block';
                        } else {
                            customSection.style.display = 'none';
                        }
                    });
                });

                // Enter key shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        RobotsTxtGenerator.generate();
                    }
                });
            });
        })();
    </script>
</body>
</html>
